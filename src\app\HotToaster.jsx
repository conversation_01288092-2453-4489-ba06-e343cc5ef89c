"use client";

import { Toaster } from "react-hot-toast";

export default function HotToaster() {
  return (
    <Toaster
      position="top-right"
      toastOptions={{
        duration: 4000,

        className: "react-hot-toast",

        success: {
          className: "react-hot-toast success-toast",
          duration: 4000,
        },
        error: {
          className: "react-hot-toast error-toast",
          duration: 5000,
        },
        loading: {
          className: "react-hot-toast info-toast",
        },
      }}
      gutter={20}
      containerStyle={{
        top: 24,
        right: 24,
      }}
      reverseOrder={true}
    />
  );
}
