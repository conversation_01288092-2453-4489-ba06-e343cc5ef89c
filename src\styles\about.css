/* About Page Styles */
.about-container {
  margin-top: 100px;
  margin-bottom: 150px;
  max-width: 900px;
  padding: 30px;
  color: var(--text);
  background: var(--box-bg);
  border-radius: 25px;
  box-shadow: rgba(0, 0, 0, 0.24) 12px 16px 24px,
    rgba(0, 0, 0, 0.24) 12px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
  transition: all 0.3s ease-in-out;
}

.about-section {
  margin-bottom: 10px;
  padding: 25px;
  background: var(--box-inner);
  border-radius: 15px;
  border: solid 1px var(--border);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.about-section:hover {
  transform: translateY(-5px);
  background: var(--box-hover);
  border: solid 1px var(--primary-color);
}

.about-section h2 {
  font-family: "Monument", sans-serif;
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--primary-color);
  font-weight: 500;
}

.about-section p {
  font-family: "Poppins", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 15px;
  color: var(--text);
}

.about-section ul {
  margin-left: 20px;
  margin-bottom: 15px;
}

.about-section li {
  font-family: "Poppins", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 8px;
  list-style-type: disc;
  color: var(--text);
}

/* Mobile-specific styles */

.mobile-about 

.mobile-about .about-section {
  padding: 15px !important;
  margin-bottom: 20px !important;
}

.mobile-about .about-section:hover {
  transform: none !important; /* Disable hover effect on mobile */
}

.mobile-about .about-section h2 {
  font-size: 1.4rem !important;
  margin-bottom: 15px !important;
}

.mobile-about .about-section p,
.mobile-about .about-section li {
  font-size: 0.95rem !important;
  line-height: 1.5 !important;
  margin-bottom: 10px !important;
}

.mobile-about .about-section ul {
  margin-left: 15px !important;
  margin-bottom: 15px !important;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Media queries */
@media (max-width: 768px) {
  .about-container {
    margin: 4vh auto 50px !important;
    padding: 15px;
    width: 95%;
    box-shadow: none;
  }

  .about-section {
    padding: 15px;
    margin-bottom: 20px;
  }

  .about-section h2 {
    font-size: 1.4rem;
  }
}
