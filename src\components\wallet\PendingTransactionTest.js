"use client";

import React, { useState } from "react";
import PendingTransaction from "./PendingTransaction";

function PendingTransactionTest() {
  const [showTransaction, setShowTransaction] = useState(false);

  const mockSwapData = {
    amount: "1000000000000000000",
    amountIn: "1000000000000000000",
    amountOut: "500000000",
    buyAmount: "500000000",
    pairRoute: "V2",
    gasGwei: "15",
    gasType: "Average",
  };

  return (
    <div
      style={{
        position: "fixed",
        bottom: "20px",
        right: "20px",
        zIndex: 1000,
      }}
    >
      <button
        onClick={() => setShowTransaction(!showTransaction)}
        style={{
          padding: "10px 20px",
          backgroundColor: "var(--primary-color)",
          color: "black",
          border: "none",
          borderRadius: "10px",
          cursor: "pointer",
          fontWeight: "bold",
        }}
      >
        {showTransaction ? "Hide" : "Show"} Transaction
      </button>

      {showTransaction && (
        <PendingTransaction
          transaction={null}
          chainId={8453}
          devMode={true}
          devStatus="pending"
          swapData={mockSwapData}
          onClose={() => setShowTransaction(false)}
        />
      )}
    </div>
  );
}

export default PendingTransactionTest;
