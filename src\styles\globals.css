@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap");

/* PP Monument Extended Font */
@font-face {
  font-family: "PP Monument Extended";
  src: url("/fonts/Monument Extended Bold/PPMonumentExtended-Bold.woff2")
      format("woff2"),
    url("/fonts/Monument Extended Bold/PPMonumentExtended-Bold.woff")
      format("woff"),
    url("/fonts/Monument Extended Bold/PPMonumentExtended-Bold.ttf")
      format("truetype");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

html,
body {
  background: #121518;
  padding: 0;
  margin: 0;
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  color: #ffffff;

  /* Disable text selection across the entire app */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

a {
  color: inherit;
  text-decoration: none;
}
:root {
  /* Font strategy: 80% Poppins, 20% Monument Extended */
  --f-family: "Poppins", sans-serif;
  --title-font: "PP Monument Extended", "Poppins", sans-serif;
  --body-font: "Poppins", sans-serif;
  --bold-font: "PP Monument Extended";

  /* Dark mode (default) */
  --text: #ffffff;
  --text-gray: rgba(222, 222, 222, 0.9);
  --text-darkgray: rgba(128, 128, 128, 1);
  --background: #151617;
  --box-bg: linear-gradient(95deg, #262a2f 0%, #181a1d 100%);
  --box-inner: linear-gradient(95deg, #1b1d1f 0%, #151618 100%);
  --border: #2a2d31;
  --box-darker: rgb(19, 19, 19);
  --box-hover: #2a2d31;

  /* light mode */
  --light-bg: #fffef7;
  --light-gray: rgba(240, 239, 230, 1); /* swap bg */
  --light-weak-grey: rgba(247, 246, 240, 1); /* TL buttons / list */
  --light-border: #d8d7cf;
  --light-button: #f7f6f0;
  --light-text-grey: #808080;

  /* Common colors */
  --primary-color: rgb(252, 224, 37);
  --secondary-rgb: rgb(230, 199, 0);
  --primary-rgb: rgb(252, 224, 37);
  --standout: #00ff00;
  --secondary-color: rgb(230, 199, 0);

  /* AppKit Button Custom Variables */
  --wui-color-accent-100: var(--primary-color);
  --wui-color-accent-090: #e6c700;
  --wui-color-accent-080: #d4b800;
  --wui-color-inverse-100: #000000;
  --wui-border-radius-m: 25px;
  --wui-box-shadow-blue: rgba(252, 224, 37, 0.2);

  /* Additional variables */
  --tab-hover: rgba(27, 29, 31);
  --dark-red: rgba(139, 0, 0, 0.9);
  --button-bg: rgba(49, 203, 158, 0.3);
  --minor-button: rgba(49, 49, 49);
  --button-gradient: linear-gradient(
    to right,
    var(--primary-color),
    var(--secondary-color),
    var(--primary-color),
    var(--secondary-color)
  );
}
.pc {
  color: var(--primary-color);
  background: var(--primary-color);
}

/* Custom Connect Button Styles - Only handling hover/active states */
.custom-connect-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15) !important;
}

.custom-connect-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Tabs group styles */
.tabs-group {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
}

/* Custom wallet button styles have been consolidated */

/* Tab Effect Styles */
.tab-effect-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  pointer-events: none;
}

/* Tab particles */
.tab-particle {
  position: absolute;
  background: linear-gradient(135deg, #fce025, #e6c700);
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(252, 224, 37, 0.4);
}

/* Tab Mode Animations */
.tab-mode-container {
  position: relative;
  display: flex;
  background: var(--box-bg);
  border-radius: 14px;
  width: fit-content;
  overflow: visible;
}
.light .tab-mode-container {
  background: #fff;
  border: 1px solid var(--light-border);
}

.tab-mode {
  padding: 6px 35px;
  font-size: 16px;
  font-family: var(--body-font);
  font-weight: 500;
  color: var(--text);
  cursor: pointer;
  margin: 0;
  position: relative;
  z-index: 1;
  border-radius: 50%;
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  user-select: none;
}
.light .tab-mode {
  color: #000;
}

.tab-mode:hover {
  transform: translateY(-1px);
}

.tab-mode:active {
  transform: translateY(1px);
}

.tab-mode-active {
  color: #000;
  cursor: pointer;
}

/* New button styles */
.action-button {
  padding: 8px 16px;
  font-size: 18px;
  font-weight: 500;
  font-family: var(--body-font);
  color: #fff;
  background: var(--box-inner);
  border: 1px solid var(--border);
  border-radius: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.light .action-button {
  background: var(--light-button);
  border: 1px solid var(--light-border);
  color: #000;
}
.light .action-button:hover {
  background: var(--light-gray);
}

.action-button:hover {
  background: var(--box-hover);
}

.action-button-active {
  color: #000;
  background: var(--primary-color);
  border: 1px solid var(--primary-color);
  font-weight: 600;
}

.action-button-active:hover {
  filter: brightness(1.1);
  background: var(--primary-color);
}
.light .action-button-active {
  background: #000;
  color: #fff;
  border: 1px solid var(--light-border);
}
.light .action-button-active:hover {
  color: #000;
  background: #fff;
}

/* Cere Dropdown Styles */
.cere-dropdown-container {
  position: relative;
  width: fit-content;
  z-index: 0;
}

.cere-dropdown-header {
  font-family: var(--body-font);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px 9px;
  border-radius: 25px;
  box-shadow: rgba(0, 0, 0, 0.24) 12px 16px 24px,
    rgba(0, 0, 0, 0.24) 12px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text);
  height: 40px;
  border: 1px solid var(--border);
  position: relative;
  z-index: 5;
  font-family: Poppins;
  font-weight: 400;
  font-size: 18px;
}
.light .cere-dropdown-header {
  color: #000;
  border: 1px solid var(--light-border);
  background: var(--light-button);
}

.cere-dropdown-header-mobile {
  min-width: auto !important;
  width: 32px !important;
  height: 32px !important;
  padding: 2px;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--primary-color);
}

/* When Cere is enabled/live */
.cere-dropdown-header-active {
  border: 1px solid var(--primary-color);
}

.cere-dropdown-header:hover {
  filter: brightness(110%);
}

.cere-dropdown-header-active:hover {
  filter: brightness(110%);
  border: 1px solid var(--primary-color);
}

.cere-logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background: transparent;
}

.cere-dropdown-header-mobile .cere-logo-container,
.network-dropdown-header-mobile .cere-logo-container {
  margin-right: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.cere-logo-image {
  object-fit: contain;
  border-radius: 50%;
}

/* Wingman container styles */
.wingman-container {
  display: flex;
  align-items: center;
  border-radius: 25px;
  padding: 5px 12px;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  height: 44px;
  background: transparent;
  border: 1px solid var(--border);
}

.wingman-container:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.wingman-mobile {
  width: 36px;
  height: 36px;
  padding: 5px;
  justify-content: center;
  border-radius: 50%;
}

.light .wingman-container {
  background-color: var(--light-button);
  color: #000;
  border: 1px solid var(--light-border);
}

.status-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #ff6e71; /* Red dot like in the image */
}
.status-dot-wrapper {
  padding: 3px;
  margin-left: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--border);
  background: var(--box-inner);
}
.light .status-dot-wrapper {
  padding: 3px;
  margin-left: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--light-border);
  background: #fff;
}

.status-dot.live {
  background-color: #00ff00; /* Green dot like in the image */
}

/* Wallet Button Styles */
.wallet-dropdown-container {
  position: relative;
}

/* Add an invisible bridge to make it easier to move to the dropdown */
.wallet-dropdown-container::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 5px;
  background: transparent;
}

.wallet-button {
  font-size: 18px;
  font-family: var(--body-font);
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px 12px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text);
  height: 40px;
  gap: 8px;
  margin: 0;
  position: relative;
  z-index: 5;
}

.wallet-button-mobile {
  min-width: auto !important;
  width: fit-content !important;
  height: 32px !important;
  padding: 3px 8px;
  font-size: 14px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wallet-button-default {
  border: 1px solid var(--primary-color);
}
.light .wallet-button-default {
  border: 1px solid var(--primary-color);
  background: var(--primary-color);
  color: #000;
}

.wallet-button-connected {
  border: 1px solid var(--primary-color);
}
.light .wallet-button-connected {
  border: 1px solid var(--light-border);
  background: var(--light-button);
  color: #000;
}

.wallet-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
}

.wallet-dropdown-content {
  position: absolute;
  top: calc(100% + 5px); /* Reduced gap for easier mouse movement */
  right: 0;
  width: 140px;
  background: var(--box-inner);
  border: 1px solid var(--border);
  border-radius: 12px;
  box-shadow: rgba(0, 0, 0, 0.24) 12px 16px 24px,
    rgba(0, 0, 0, 0.24) 12px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
  z-index: 100;
  overflow: hidden;
  animation: fadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  padding-top: 3px; /* Add padding to increase clickable area */
  padding-bottom: 3px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.wallet-dropdown-item {
  padding: 12px 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.wallet-dropdown-item:hover {
  background: var(--box-hover);
}

.wallet-dropdown-item.disconnect {
  color: #ff5555;
}

.wallet-dropdown-item.disconnect:hover {
  background: rgba(255, 85, 85, 0.1);
}

.wallet-dropdown-item.disconnect.disconnecting {
  opacity: 0.7;
  cursor: wait;
  background: rgba(255, 85, 85, 0.05);
}

.wallet-dropdown-item.disconnect.disconnecting:hover {
  background: rgba(255, 85, 85, 0.05);
}

/* Theme Toggle Styles */
.theme-toggle-container {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin: 0 10px;
}

.theme-toggle {
  position: relative;
  width: 48px;
  height: 26px;
  border-radius: 13px;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
}

.dark .theme-toggle {
  background-color: #333;
  border: 1px solid #444;
}

.light .theme-toggle {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

.toggle-thumb {
  position: absolute;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Logo specific styles */
.light .logo-image {
  filter: brightness(0.9);
}

.dark .logo-image {
  filter: brightness(1);
}

.light .splash-logo {
  filter: drop-shadow(0 0 15px rgba(252, 224, 37, 0.7)) !important;
}

.dark .splash-logo {
  filter: drop-shadow(0 0 20px rgba(252, 224, 37, 0.9)) !important;
}

/* Box specific styles */
.box {
  background: var(--box-bg);
  border: 1px solid var(--border);
  color: var(--text);
}

/* Button specific styles */
.light .button {
  background: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.dark .button {
  background: #333;
  color: #fff;
  border: 1px solid #444;
}

/* Tab mode active styles */
.tab-mode-active {
  color: #000;
  cursor: pointer;
}

.light .tab-mode-active {
  color: #fff;
}

/* Network Transition Overlay */
.network-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 1;
  transition: opacity 0.5s ease-out;
  will-change: opacity;
  pointer-events: all;
}

.network-transition-overlay.fade-out {
  opacity: 0;
}

.network-transition-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  padding: 30px;
}

.network-transition-spinner {
  width: 80px;
  height: 80px;
  border: 4px solid rgba(252, 224, 37, 0.2);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 0.8s linear infinite;
  margin-bottom: 20px;
  box-shadow: 0 0 10px rgba(252, 224, 37, 0.5);
}

.network-transition-text {
  font-size: 20px;
  font-weight: 600;
  color: var(--text);
  font-family: var(--body-font);
  text-shadow: 0 0 5px rgba(252, 224, 37, 0.3);
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.network-transition-subtext {
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-color);
  font-family: var(--body-font);
  opacity: 0.9;
  margin-top: 5px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Nav buttons group styles */
.nav-buttons-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
  height: 40px; /* Set a fixed height to ensure consistent alignment */
}

.mobile-buttons-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
  width: 100%;
  margin-top: 10px;
}

.arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 2px;
  padding-left: 8px;
  position: relative;
}

.dropdown-arrow {
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

.cere-dropdown-content {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  width: 250px;
  background: var(--box-inner);
  border-radius: 12px;
  padding: 16px;
  z-index: 20;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: fadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.light .cere-dropdown-content {
  background: var(--light-gray);
  border: 1px solid var(--light-border);
  box-shadow: none;
  color: #000;
}

.cere-info {
  margin-bottom: 16px;
}
.light .cere-info {
  color: #000;
}
.light .cere-info p {
  color: #000;
}

.cere-info h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text);
}
.light .cere-info h4 {
  color: #000;
}

.cere-info p {
  margin: 0;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-gray);
  font-family: "poppins";
}

/* Network Dropdown Styles */
.network-dropdown-container {
  margin-right: 10px;
}

.network-dropdown-header {
  font-size: 18px;
  font-family: var(--body-font);
  font-weight: 500;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 3px 9px;
  border-radius: 25px;
  box-shadow: rgba(0, 0, 0, 0.24) 12px 16px 24px,
    rgba(0, 0, 0, 0.24) 12px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text);
  height: 40px;
  border: 1px solid var(--border);
  min-width: 120px;
  position: relative;
  z-index: 5;
}

.network-dropdown-header-mobile {
  min-width: auto !important;
  width: 32px !important;
  height: 32px !important;
  padding: 2px;
  justify-content: center;
  border-radius: 50%;
}

.network-dropdown-content {
  width: 280px;
}

.network-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.network-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 12px;
  background: var(--box-inner);
  border: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.network-item:hover {
  background: var(--box-hover);
  transform: translateY(-1px);
}

.network-item-active {
  border: 1px solid var(--primary-color);
}

.network-item-active:hover {
  border: 1px solid var(--primary-color);
}

.network-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background: transparent;
}

/* Mobile adjustments for network dropdown */
@media (max-width: 768px) {
  .network-dropdown-header {
    padding: 3px 6px;
    min-width: auto;
    width: fit-content;
  }

  .network-dropdown-header span {
    font-size: 16px;
  }

  .network-dropdown-container {
    border: 1px solid var(--primary-color);
    border-radius: 50%;
    margin-right: 5px;
  }

  .cere-dropdown-content,
  .network-dropdown-content {
    position: fixed;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 300px;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    border: 1px solid var(--primary-color);
    animation: mobileDropdownFadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* Keep wallet dropdown positioned relative to the wallet button */
  .wallet-dropdown-content {
    position: absolute;
    top: calc(100% + 5px);
    right: 0;
    width: 140px;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  @keyframes mobileDropdownFadeIn {
    from {
      opacity: 0;
      transform: translate(-50%, -30px);
    }
    to {
      opacity: 1;
      transform: translate(-50%, 0);
    }
  }

  /* About page styles moved to about.css */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.glider {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(135deg, #fce025, #e6c700);
  border-radius: 25px;
  z-index: 0;
}
.light .glider {
  background: #000;
}

/* Removed .move-right class as we're using framer-motion x transform instead */

/* Juiced mode styles consolidated below */

/* Removed background from juiced mode since glider handles it */
.juiced-mode-bg {
  position: relative;
}

/* Custom input inline styles */
.custom-input-inline {
  width: 18%; /* Match the option-button width */
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-input-small {
  width: 100%;
  height: 30px;
  background: transparent;
  border: solid 2px var(--primary-color);
  border-radius: 25px;
  color: var(--text);
  font-family: var(--body-font);
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  padding: 0 5px;
  outline: none;
  box-shadow: 0 0 5px rgba(252, 224, 37, 0.3);
  transition: all 0.2s ease;
}

.custom-input-small:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 8px rgba(252, 224, 37, 0.5);
}

/* Global styles.css or in a global <style jsx> tag */

/* Styling the scrollbar for webkit browsers (Chrome, Safari, newer versions of Edge) */
/* Styling the scrollbar for webkit browsers (Chrome, Safari, newer versions of Edge) */
/* Styling the scrollbar for webkit browsers (Chrome, Safari, newer versions of Edge) */
::-webkit-scrollbar {
  width: 4px; /* Width of the scrollbar */
  height: 4px; /* Height of the scrollbar */
}

::-webkit-scrollbar-track {
  background: transparent; /* Making the track background transparent */
}

::-webkit-scrollbar-thumb {
  background: rgba(
    255,
    255,
    255,
    0.2
  ); /* Light gray color for the scrollbar thumb */
  border-radius: 10px; /* Rounded corners */
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5); /* Slightly darker on hover */
}

/* For Firefox */
* {
  box-sizing: border-box;

  scrollbar-width: thin; /* "auto" or "thin" */
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent; /* Thumb and track color */
}

.font_heading,
.t_lang-heading-default .font_heading {
  font-family: var(--title-font);
  font-size: 36px;
  font-weight: bold;
  line-height: 44px;
}

.font_subHeading,
.t_lang-subHeading-default .font_subHeading {
  font-family: var(--body-font);
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
}

.font_body,
.t_lang-body-default .font_body {
  font-family: var(--body-font);
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;
}

.font_button,
.t_lang-button-default .font_button {
  font-family: var(--body-font);
  font-size: 18px;
  font-weight: 500;
  line-height: 18.4px;
}

.blur {
  filter: blur(2px);
  pointer-events: none;
}
/* Container for the select and custom arrow */
/* Container styles */

.swap-upper-box {
  background: var(--box-inner);

  border-radius: 25px;
  padding: 8px 15px;
}
.swap-upper {
  transition: all 0.2s ease-in-out;
  padding: 10px;
  background: var(--box-bg);

  border-radius: 25px;
  display: flex;
  position: absolute;
  top: 27px;
  right: 10px;
  flex-direction: column;
  min-width: 270px;
  z-index: 4;
  overflow: visible;
  gap: 8px;
}

.upper-settings {
  margin-bottom: 0.5rem;
}

.auto-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Common styles for dropdown wrapper */
.dropdown-wrapper {
  position: relative;
  width: 110px; /* Adjust as needed */
  font-family: sans-serif;
}

/* Header that shows the current value */
.dropdown-header {
  padding: 5px 10px;
  background: var(--box-darker);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 15px;
}

/* Custom arrow styling */
.dropdown-arrow {
  transition: transform 0.3s ease;
  color: var(--primary-color);
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  transform-origin: center;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

/* Options container */
.dropdown-options {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  width: 100%;
  background: var(--box-inner);

  border-top: none;
  border-radius: 15px;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Each option */
.dropdown-option {
  padding: 0.5rem;
  cursor: pointer;
}

.dropdown-option:hover {
  background: var(--box-hover);
}

/* Icon container for consistent spacing */
.settings-icon-container {
  margin-right: 0.5rem;
}

/* Text settings */
.us-ts {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-gray);
}

/* Rotate the arrow when the select is focused */

/* Tab mode styles moved to the top section */

.tab-selector {
  position: absolute;
  bottom: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  width: 100%;
  height: 40px;
  padding-top: 5px;
  background: var(--box-bg);
  z-index: 1;
  border-bottom-left-radius: 25px;
  border-bottom-right-radius: 25px;
}

/* The sliding glider */
.glider-chart {
  position: absolute;
  top: 5px; /* matches .tab-selector's padding-top if needed */
  left: 0px; /* default: behind first tab */
  width: 50%;
  height: 35px; /* matches .tab-button height */
  background-color: var(--primary-color);
  border-radius: 5px;
  z-index: 0; /* behind the tab buttons */
  transition: left 0.2s ease; /* smooth slide */
}

/* Each tab button */
.tab-button {
  color: var(--text);
  margin-bottom: 5px;
  font-size: 1rem;
  font-weight: 700;
  border-radius: 5px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 50%;
  height: 35px;
  border: none;
  box-sizing: border-box;
  transition: all 0.1s ease-in-out;
  z-index: 1; /* ensure the text is above the glider */
}
.tba {
  color: #000;
}

.tab-button:hover {
  background: var(--box-hover-light);
  cursor: pointer;
}

input:focus {
  outline: none;
  transition: all 0.1s ease-in-out;
}
.switch-item-container {
  padding-inline: 5px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.row {
  display: flex;
  flex-direction: row;
}
.basic-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  color: var(--text);
  font-size: 1.1rem;
  transition: all 0.1s ease-in-out;
  opacity: 0.8;
  transition: all 0.1s ease-in-out;
  letter-spacing: 1px;
  text-align: center;
}
.basic-button:hover {
  cursor: pointer;
  opacity: 1;
}

.toggle-switch {
  margin-left: 5px;
  box-shadow: 0 1px 3px var(--primary-color);
  border-radius: 10px;
}

.mempool-container {
  display: flex;
  justify-content: space-between;
  padding: 0px;
  width: 100%;
}

.gas-tracker {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.gas-price-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: var(--box-darker);
  color: var(--text-gray);
  font-weight: 400;
  padding: 3px;
  border-radius: 10px;
  text-align: center;
  border: solid 1px var(--border);
  font-size: 0.9rem;
}
.Pineapple {
  background: var(--box-hover);
  color: var(--text);
}

.gas-price-content {
  padding: 2px;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  font-size: 0.9rem;
  text-align: center;
  outline: none;
  transition: all 0.1s ease-in-out;
  border: none;
}
.beta {
  position: relative;
  font-size: 1.5rem;
  width: fit-content;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 5px;
  z-index: 100;
}
/* .bg {

  position: absolute;
  pointer-events: none;
  opacity: 0.4;
  width: 100%;
  height: 100%;
  background-image: url("/bg-1.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
} */

.beta-text {
  font-size: 1rem;
  color: white;
  font-weight: 500;
}
.intro-container {
  z-index: 100;
  display: flex;
  position: fixed;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  background-color: var(--box-bg);
  opacity: 0.95;
  transition: all 4s ease-in-out;
  background-image: url("https://i.ibb.co/XyMp9pj/light.gif");
  background-size: cover;
}
.intro-box {
  display: flex;
  flex-direction: column;
  padding: 15px;
  align-items: center;
  justify-content: center;
  border: solid 1px var(--border);
  background: var(--box-inner);
  border-radius: 10px;
  transition: all 0.3s ease-in-out;
}
.intro-box:hover {
  background: var(--secondary-color);
}
.banner-container {
  width: 100%;
  height: 120px; /* Fixed height */
}

.intro-banner {
  background-image: url("https://i.ibb.co/PNQ8KmF/photo-2024-03-27-21-10-04.jpg");
  background-size: cover; /* Cover the area of the div, may clip the image */
  background-position: center; /* Center the background image */
  position: relative;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  width: 100%; /* Take full width of its container */
  height: 100%; /* Take full height of its container, which is set by .banner-container */
}
.load-container {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
  pointer-events: all;
  background-color: var(--background);
  z-index: 9999;
}
.whole-container {
  position: relative;
  background: var(--background);

  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
  overflow-x: hidden; /* Prevent horizontal overflow during chart slide-in */
}
.light .whole-container {
  background: var(--light-bg);
}
.main-container {
  padding-bottom: 10vh;
  padding-left: 15px;
  padding-right: 15px;
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  height: 100%;
  width: 98%;
  gap: 15px;
  overflow-x: hidden; /* Prevent horizontal overflow during chart slide-in */
}

.page-container {
  padding-top: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 90vh;
  width: 100%;
  color: white;
}
.saver-info-container {
  /*   background: var(--box-darker);
  border: solid 1px var(--border);
 */
  border-radius: 15px;
  position: relative;
  padding: 2px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  transition: all 0.3s ease-in-out;
}
.saver-text-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 2px;
  padding: 2px;
}
.saver-text {
  font-size: 1rem;
  color: var(--primary-color);
}
.saver-text-left {
  font-size: 0.9rem;
  color: var(--text-gray);
}
.saver-text-right {
  font-size: 0.9rem;
  color: var(--text);
  font-weight: 500 !important;
}
.saver-icon-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 30px;
}
.settings-icon-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 25px;
  width: 25px;
  margin-right: 5px;
}
.bottom-container {
  padding-top: 15px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  height: 90vh;
  width: 98%;
  color: white;
  transition: all 0.3s ease-in-out;
}
.nav-container {
  backdrop-filter: blur(10px);
  height: fit-content;
  margin-top: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: white;
  z-index: 5;
  padding: 20px 30px;
}

.nav-left {
  display: flex;
  align-items: center;
  width: 33%;
}

.nav-right {
  display: flex;
  justify-content: flex-end;
  flex-direction: row;
  align-items: center;
  height: 40px; /* Fixed height to ensure consistent alignment */
}

.block-text {
  font-size: 14px;
  font-weight: 400;
  color: var(--text-gray);
  position: absolute;
  display: flex;
  align-items: center;
  gap: 2px;
}
.light .block-text {
  color: #000;
}

.footer-container {
  position: fixed;
  /*   background: rgba(0, 0, 0, 0.4);
 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  bottom: 0px;
  left: 0;
  right: 0;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  color: white;
  z-index: 5;
}
.light .footer-container {
}
.footer-left span {
  font-weight: 500;
}

.footer-left {
  color: rgba(134, 132, 124, 1);
  font-size: 18px;
  font-weight: 500;
}

/* .swap-container:hover {
  transform: scale(1.01);
} */
.stop1 {
  stop-color: #fce025;
}

.stop2 {
  stop-color: #e6c700;
}

.swap-tokens {
  position: absolute;
  width: 100%;
  height: 0;
  left: 0;
  top: 50%;
  margin: -5px 0 0 0; /* Slight adjustment to center it perfectly */
  overflow: visible;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-top: 2px;
  padding-bottom: 2px;
}
.bt {
  padding-top: 5px;
  border-top: solid 1px var(--border);
}
.light .bt {
  border-top: solid 1px var(--light-border);
}

.gas-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.gas-box-top {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #1a1b1f;
  padding: 12px;
  font-weight: 500;
  height: 37.6px;
  border-radius: 10px;
}
.slip-box {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 60%;
}
.gas-input-box {
  width: 90%;
  background: var(--box-inner);
  color: var(--text);
  font-size: 1rem;
  padding-inline-start: 5px;
  font-weight: 500;
  border-radius: 15px;
  border: solid 1px var(--border);
}

.small-input-container {
  margin-top: 2px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  border-radius: 15px;
  background-color: var(--box-inner);
  border: none;
  color: var(--text);
}

.small-title {
  font-size: 1.1rem;
  font-weight: 500;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  padding-inline-start: 6px;
  align-items: center;
  width: 100%;
  color: var(--text-gray);
}

.slip-buttons {
  padding-top: 3px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  border-radius: 15px;
  color: var(--text);
}
.slip-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 45%;
  height: 23px;
  border-radius: 25px;
  background-color: var(--secondary-color);
  border: none;
  color: var(--text);
  font-size: 0.8rem;
  font-weight: 500;
}
.slip-button:hover {
  background-color: var(--box-inner);
  color: var(--text);
  cursor: pointer;
}
.gas-buttons {
  padding-top: 3px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: space-between;
  width: 100%;
  height: 100%;
  border-radius: 25px;

  color: var(--text);
}
.gas-button {
  margin-top: 3px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 80%;
  height: 20px;
  border-radius: 25px;
  background-color: var(--secondary-color);
  border: none;
  color: var(--text);
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.1s ease-in-out;
}

.gas-button:hover {
  background-color: var(--box-inner);
  color: var(--text);
  cursor: pointer;
}

.flex-col {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-top: 2px;
  padding-bottom: 2px;
  z-index: 1;
}

.token-input-box {
  gap: 5px;
  display: flex;
  position: relative;
  padding: 16px 18px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  border-radius: 35px;
  background: var(--box-inner);
  color: var(--text);
  transition: all 0.2s ease-in-out;
}
.light .token-input-box {
  background: var(--light-weak-grey);
  border: 2px solid var(--light-border);
}
.token-input-box-top {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-top: 10px;
  padding-bottom: 10px;
}
.token-input-box:hover {
  filter: brightness(1.1);
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.token-input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
  background-color: transparent;
  color: var(--text);
  outline: none;
  border: none;
  font-size: 42px;
  font-weight: 300;
  width: 90%;
  margin-top: 1px;
  margin-bottom: 1px;
  padding: 0;
  font-family: var(--body-font);
}
.light .token-input[type="number"] {
  color: #000;
}

.light .token-input[type="text"] {
  font-weight: 300;
  -moz-appearance: textfield;
  appearance: textfield;
  background-color: transparent;
  color: var(--text);
  outline: none;
  border: none;
  font-size: 1.7rem;
  width: 60%;
  margin-top: 1px;
  margin-bottom: 1px;
  padding: 0;
}
.listing-input[type="text"] {
  font-weight: 500;
  -moz-appearance: textfield;
  appearance: textfield;
  background-color: transparent;
  color: var(--text);
  outline: none;
  border: none;
  font-size: 1rem;
  width: 100%;
  margin-top: 1px;
  margin-bottom: 1px;
  padding: 0;
}
.listing-text {
  padding-inline-start: 10px;
  font-weight: 500;
  -moz-appearance: textfield;
  appearance: textfield;
  background-color: transparent;
  color: var(--text);
  outline: none;
  border: none;
  font-size: 1rem;
  width: 100%;
  margin-top: 1px;
  margin-bottom: 1px;
}
.token-select-box {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  background-color: rgb(19, 19, 19);
  color: rgb(255, 255, 255);
  cursor: pointer;
  height: 36px;
  border-radius: 18px;
  outline: none;
  user-select: none;
  /*   border: 1px solid rgba(255, 255, 255, 0.07);
 */
  font-size: 1.2rem;
  width: initial;
  padding: 4px 8px 4px 4px;
  gap: 8px;
  justify-content: space-between;
  margin-left: 12px;
  box-shadow: rgba(34, 34, 34, 0.04) 0px 0px 10px 0px;
  visibility: visible;
  white-space: nowrap; /* Forces the text to stay on one line */
  overflow: hidden; /* Hides any overflow text */
  text-overflow: ellipsis; /* Adds '...' to indicate hidden text */
}
.light .token-select-box {
  background-color: var(--light-gray);
  border: 2px solid var(--light-border);
  color: #000;
}

.token-select-box:hover {
  background-color: var(--tab-hover);
}
.token-select-box:focus {
  border: solid 1px var(--primary-color);
}
.tokens-select-container {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 10px; /* Reduced space between the token inputs */
}

.med-text {
  margin-left: 0px;
  font-size: 18px;
  text-align: center;
  color: var(--text);
  overflow: hidden;
  font-weight: 400;
}
.light .med-text {
  color: #000;
}
.swap-arrow {
  position: absolute;

  z-index: 1;
}

.token-list {
  margin: 0;
  background-color: var(--box-darker);
  border: 1px solid rgba(255, 255, 255, 0.07);
  box-shadow: rgba(0, 0, 0, 0.24) 12px 16px 24px,
    rgba(0, 0, 0, 0.24) 12px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
  padding: 8px 0px;
  width: 400px;
  overflow: hidden auto;
  height: 90vh;
  max-height: 700px;
  display: flex;
  flex-direction: column;
  border-radius: 20px;
}
.token-list-top {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
  top: 0;
  gap: 10px;
  position: sticky;
  border-bottom: 1px solid var(--border);
  z-index: 2;
}
.token-list-items {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0px;
  margin: 0px;
}

/* Initial hidden state */
.token-list-container {
  position: fixed;
  z-index: 20;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
  pointer-events: all;
  opacity: 0; /* Start hidden */
  transition: all 0.2s ease-in-out; /* Apply the fade-in transition */
  transition: opacity 0.3s ease-in-out; /* Apply the fade-in transition */
}

/* Fade-in class */
.token-list-container.fade-in {
  opacity: 1; /* Make fully visible */
}

.port-token-list {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: max-content;
  overflow-x: hidden;
  background-color: var(--box-darker);
  border: 1px solid var(--border);

  box-shadow: 0 5.40728px 10.8146px rgba(0, 0, 0, 0.3);
}

.token-list-item {
  font-weight: 500;
  color: var(--text);
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px;
  border-radius: 2px;
  cursor: pointer;
  margin: 0;
}

.token-list-item:hover {
  background-color: var(--box-hover);
}

.token-list-item-image {
  margin-right: 10px;
  padding-left: 3px;
}

.token-list-item-text {
  display: flex;
  flex-direction: column;
}

.token-list-item-text-name {
  color: var(--text);
  text-align: end;
  overflow: hidden;
  font-size: 1rem;
}

.token-list-item-text-symbol {
  color: var(--text-gray);
  font-size: 0.9rem;
}
.hide-element {
  display: none;
  opacity: 0;
  transition: all 0.3s ease-in-out;
}

.quote-container {
  margin-bottom: 3px;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 500px;
  min-height: 200px;
  height: fit-content;
  border-radius: 25px;
  opacity: 1;
  z-index: 0;
  /*   max-width: 320px;
 */ /*   overflow: hidden;
 */
}
/* Removed empty ruleset */
.hide {
  opacity: 0;
  border: none;
  outline: none;
  height: 1px;
  width: 95%;
  overflow: hidden;
}
.quote-line {
  font-size: 0.7rem;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: var(--box-darker);
  box-shadow: var(--box-shadow);
  margin-top: 2px;
  padding: 4px;
  border-radius: 5px;
}
.quote-line:hover {
  background-color: var(--box-hover);
}

.quote-title {
  font-size: 0.9rem;
  color: var(--text);
}

.quote-content {
  position: relative;
  font-size: 0.9rem;
  color: var(--text-gray);
}
.swap-button {
  margin-top: 10px;
  margin-bottom: 5px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 60px;
  border-radius: 35px;
  border: 1px solid rgba(255, 165, 0, 0.3);
  color: #000;
  font-size: 20px;
  font-weight: 600;
  transition: all 0.2s ease;
  background: var(--primary-color);
}
.swap-button:hover {
  background: var(--primary-color);
  filter: brightness(1.05);
  cursor: pointer;
}
.connect-button {
  margin-top: 10px;
  margin-bottom: 5px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 150px;
  height: 35px;
  border-radius: 10px;
  color: var(--text);
  font-size: 1.1rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  opacity: 0.9;
  border: solid 1px var(--primary-color);
  background: var(--box-inner);
  box-shadow: rgba(0, 0, 0, 0.24) 8px 12px 18px,
    rgba(0, 0, 0, 0.24) 8px 6px 10px, rgba(0, 0, 0, 0.32) 3px 3px 6px;
}
.connect-button:hover {
  box-shadow: rgba(0, 0, 0, 0.24) 10px 14px 20px,
    rgba(0, 0, 0, 0.24) 10px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
  color: var(--primary-color);
  cursor: pointer;
  opacity: 1;
  background: var(--box-hover);
}
.audit-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 32px;
  border-radius: 10px;
  color: var(--text);
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  opacity: 0.9;
  padding-inline: 10px;
  border: solid 1px var(--border);
  background: var(--box-inner);
  box-shadow: rgba(0, 0, 0, 0.24) 8px 12px 18px,
    rgba(0, 0, 0, 0.24) 8px 6px 10px, rgba(0, 0, 0, 0.32) 3px 3px 6px;
}
.audit-button:hover {
  cursor: pointer;
  opacity: 1;
  color: var(--primary-color);
  background: var(--box-hover);
  box-shadow: rgba(0, 0, 0, 0.24) 10px 14px 20px,
    rgba(0, 0, 0, 0.24) 10px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
}
.mev-button {
  margin-bottom: 5px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 2px 8px;
  border-radius: 10px;
  border: solid 1px var(--primary-color);
  color: var(--text);
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.1s ease-in-out;
}
.mev-button:hover {
  background-color: var(--primary-color);
  color: var(--text);
  cursor: pointer;
}

.disable {
  z-index: 10;
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-import {
  background: var(--box-bg);
  color: var(--text);
  font-size: 1rem;
  padding: 12px;
  font-weight: 500;
  height: 37.6px;
  border-radius: 10px;
  outline: none;
  border: none;
  width: 300px;
  text-align: center;
}
.quick-import-bar {
  background: rgb(27, 27, 27);
  position: relative;
  display: flex;
  padding: 16px 16px 16px 40px;
  height: 40px;
  -webkit-box-align: center;
  align-items: center;
  width: 100%;
  white-space: nowrap;
  outline: none;
  border-radius: 12px;
  color: rgb(255, 255, 255);
  border: 1px solid rgba(255, 255, 255, 0.07);
  appearance: none;
  font-weight: 500;
  font-size: 18px;
  transition: border 100ms;
}
.inputWithIcon {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.inputIcon {
  z-index: 1;
  color: var(--text-gray);
  position: absolute;
  left: 10px; /* Adjust based on your design */
  pointer-events: none; /* Prevent icon from blocking input interaction */
}
.inputIcon {
  z-index: 1;
  color: var(--text-gray);
  position: absolute;
  left: 10px; /* Adjust based on your design */
  pointer-events: none; /* Prevent icon from blocking input interaction */
}

.quickImportBar {
  width: 100%;
  padding-left: 35px; /* Add padding to make room for the icon */
  padding-right: 10px;
  height: 2.5em;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 1em;
}

/* PendingTransaction styles */
.pending-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  pointer-events: all;
}

.pending-box {
  background-color: var(--box-inner);
  border: 1px solid var(--border);
  border-radius: 20px;
  padding: 24px;
  width: 400px;
  max-width: 90%;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.pending-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px 0;
}

.pending-text {
  margin-top: 16px;
  font-size: 18px;
  font-weight: 500;
  color: var(--text);
}

.close-button {
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 24px;
  color: var(--text-gray);
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-button:hover {
  color: var(--text);
}

.explorer-button {
  display: inline-block;
  padding: 10px 16px;
  background-color: transparent;
  border: 1px solid var(--primary-color);
  border-radius: 12px;
  color: var(--primary-color);
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.explorer-button:hover {
  background-color: rgba(252, 224, 37, 0.1);
}

.status-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Removed empty ruleset */
.new-listing {
  display: none;
  overflow: visible;
  outline: none;
  position: absolute;
  text-align: center;
  cursor: none;
  top: -16px;
  width: fit-content;
  padding: 1px 5px;
  font-size: 0.8rem;
  border-radius: 5px;
  background: var(--box-darker);
  border: none;
  color: var(--text);
  font-weight: 300;
  transition: all 0.3s ease-in-out;
}
.max-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: fit-content;
  padding: 0px 10px;
  border-radius: 5px;
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 500;
  margin-right: 4px;
  height: 15px;
  opacity: 0.7;
}

.max-button:hover {
  cursor: pointer;
  opacity: 1;
  background-color: var(--box-inner);
}
.max-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 6px;
  align-items: center;
}

.back-to-top {
  position: fixed;
  bottom: 100px;
  left: 20px;
  z-index: 1000;
  cursor: pointer;
  transition: all 0.5s ease-in-out;
  text-align: center;
  background-color: #235144;
  color: var(--text);
  border-radius: 15px;
  width: 50px;
  height: 50px;
  outline: none;
  font-size: 2rem;
}
.dollar-value {
  margin-right: 3px;
}
.nav-left {
  display: flex;
  position: relative;
  top: 0px;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  height: 100%;
  gap: 0;
  width: fit-content;
}
.nav-center {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: var(--text-gray);
}

.light .nav-center {
  color: var(--light-text-grey);
}
.click-here-footer {
  color: var(--text-gray);
  cursor: pointer;
  text-decoration: underline;
  margin-left: 4px;
}
.light .click-here-footer {
  color: #000;
}
.nav-right {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding-inline: 0px;
  height: 100%;
  width: fit-content;
  font-size: 14px;
}

/* .nav-right > * {
  margin-right: 20px;
} */
.nav-item {
  margin-left: 3px;
  margin-right: 3px;
}

/* .gif-bg {
  position: fixed;
  width: 100vw;
  height: 100vh;
  z-index: 0;
  background-image: url('https://i.ibb.co/XyMp9pj/light.gif');
  background-size: cover;
  border-radius: 15px;
  opacity: 0.2;
  transition: all 0.2s ease-in-out;
} */
.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}

.coin-watch {
  z-index: 10;
  position: fixed;
  bottom: 0px;
  left: 0;
  width: 100vw;
  height: 8vh;
  background-color: var(--box-inner);
  border: solid 1px var(--border-color);
}
.audit-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border-radius: 20px;

  background-color: var(--box-inner);
}

.audit-content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow-y: scroll;
  top: 0;
  right: 0;
  height: 100%;
  padding: 10px;
  border-radius: 20px;
  background-color: var(--box-inner);
}

.temp-text {
  position: absolute;
  left: 0;
  bottom: -5px;
  color: var(--text);
  font-size: 0.6rem;
  font-weight: 500;
  transition: all 0.1s ease-in-out;
}
.iconLinksContainer {
  margin-left: 5px;
  display: flex;
  gap: 20px; /* Adjust the space between icons as needed */
  align-items: center;
  justify-content: center;
}

.iconLink {
  color: var(--text-gray);
  transition: all 0.3s ease-in-out;
}
.iconLink:hover {
  color: var(--primary-color);
}
.icons-container {
  display: flex;
  flex-direction: row;
  gap: 20px; /* Adjust the space between icons as needed */
  align-items: center;
  justify-content: center;
}
.swap-icons-container {
  position: absolute;
  width: 100%;
  top: -50px;
  left: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  overflow: visible;
  padding-inline: 10%;
}
.swap-icons-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.audit-icons {
  z-index: 1;
  pointer-events: all;
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  flex-direction: row;
}

.audit-icons > *:not(:last-child) {
  padding-right: 10px;
}

.swap-icons-right {
  position: absolute;

  right: 10%;
  gap: 15px; /* Adjust the space between icons as needed */
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.swap-icon {
  color: var(--primary-color); /* Make settings icon yellow */
  transition: all 1s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}
.light .swap-icon {
  color: #000;
}

.swap-icon:hover {
  transition: all 5s ease-in-out;
  transform: rotate(360deg);
  filter: brightness(1.1);
  cursor: pointer;
}

/* Mobile styles for swap settings */
@media (max-width: 768px) {
  .swap-icon {
    width: 24px;
    height: 24px;
    padding: 0;
  }

  .swap-icons-right {
    position: absolute;
    top: 5px;
    right: 15px;
    z-index: 10;
  }

  .swap-icons-container {
    top: -25px;
    padding-inline: 15px;
    z-index: 10;
  }
}

.icon {
  width: 24px; /* Adjust icon size as needed */
  height: 24px; /* Adjust icon size as needed */
  fill: currentColor; /* Icon color inherits from the anchor tag */
  transition: all 0.1s ease-in-out;
}
.icon:hover {
  transform: scale(1.1);
  cursor: pointer;
}
.logo-text {
  color: var(--button-gradient);
  background: linear-gradient(90deg, #ffa500, #ff4500);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  width: 100%;
  font-size: 1.2rem;
  font-weight: 500;
  letter-spacing: 1px;
}
.saver-container {
  opacity: 0.4;
  margin-top: 3px;
  margin-bottom: 3px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 5px;
  border-radius: 15px;
  /*   background: var(--box-darker);
  border: solid 1px var(--border);
 */
  color: var(--text);
  transition: all 0.3s ease-in-out;
}
.saver-container:hover {
  opacity: 1;
}
.saver-text-gray {
  color: var(--text-gray);
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: 1px;
  width: 100%;
  text-align: center;
}

.saver-text {
  color: var(--button-gradient);
  background: linear-gradient(90deg, #ffa500, #ff4500);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: 1px;
  display: inline; /* Make sure it doesn't break lines unnecessarily */
}
.beta-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 5px;
  border-radius: 8px;
  color: var(--text);
  transition: all 0.3s ease-in-out;
}
.beta-text {
  color: white;
  font-size: 0.6rem;
  text-align: center;
  letter-spacing: 7px;
}

.swap-tabs-container {
  position: relative;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: row;
  width: fit-content;
  gap: 10px;
}

/* Juiced tabs container styles moved below */

.swap-tab {
  display: flex;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  align-items: center;
  justify-content: center;
  padding-inline: 14px;
  padding-bottom: 10px;
  height: 30px;
  padding-top: 8px;
  color: var(--text);
  font-family: var(--body-font);
  font-weight: 500;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
}
.swap-tab:hover {
  filter: brightness(1.1);
}
.swap-tab-active {
  color: #000;
  background: var(--primary-color);
}

.juiced-tab {
  padding: 0;
  height: 28px;
  font-size: 14px;
  position: relative;
  z-index: 1;
  background: transparent;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  text-align: center;
  border-bottom: none;
  font-family: var(--body-font);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation: tabFadeIn 0.8s ease-out forwards;
  animation-delay: calc(0.2s * var(--tab-index, 1));
  opacity: 0;
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

@keyframes tabFadeIn {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }
  40% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.juiced-tab:nth-child(2) {
  --tab-index: 1;
}
.juiced-tab:nth-child(3) {
  --tab-index: 2;
}
.juiced-tab:nth-child(4) {
  --tab-index: 3;
}

.juiced-tab:hover {
  transform: translateY(-2px);
}

.juiced-tab.swap-tab-active {
  color: #000;
  background: transparent;
  font-weight: 600;
}

.juiced-tabs-container {
  position: relative;
  display: flex;
  flex-direction: row;
  width: fit-content;
  gap: 0;
  background: var(--box-bg);
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  padding: 2px;
  box-shadow: rgba(0, 0, 0, 0.15) 0px 4px 12px 0px;
  overflow: visible;
  justify-content: space-evenly;
  min-width: 190px;
  border-bottom: none !important;
  animation: slideFromRight 1s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  transform-origin: right center;
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

@keyframes slideFromRight {
  0% {
    opacity: 0;
    transform: translateX(30px) scale(0.95);
    filter: blur(5px);
  }
  30% {
    opacity: 0.5;
    filter: blur(3px);
  }
  70% {
    opacity: 0.8;
    filter: blur(1px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}

.juiced-glider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 60px;
  height: calc(100% - 4px);
  background-color: var(--primary-color);
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  z-index: 0;
  border: none;
  border-bottom: none;
  transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 0 15px rgba(252, 224, 37, 0.4);
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* JuicedModeSettings styles */
.juiced-settings-container {
  display: flex;
  flex-direction: column;
  margin-top: 0px;
  margin-bottom: 10px;

  border-radius: 20px;
  width: 100%;
  gap: 10px;
  animation: morphIn 0.8s ease-out forwards;
  transform-origin: top center;
  will-change: transform, opacity;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

@keyframes morphIn {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
    filter: blur(5px);
  }
  20% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

.slippage-section,
.gas-section {
  display: flex;
  flex-direction: column;
  gap: 1px;
  animation: slideIn 1s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.gas-section {
  animation-delay: 0.4s;
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  30% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.settings-label {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.small-text {
  font-family: var(--body-font);
  color: var(--text-gray);
  font-size: 16px;
  font-weight: 400;
}
.light .small-text {
  color: #000;
}
.small-text-grey {
  font-family: var(--body-font);
  color: var(--text-gray);
  font-size: 14px;
  font-weight: 400;
}

/* Percentage buttons for input fields */
.percentage-buttons {
  display: flex;
  gap: 4px;
  justify-content: flex-start;
}

.percentage-button {
  width: auto;
  background: var(--box-inner);
  border: solid 1px var(--border);
  border-radius: 12px;
  color: var(--text-gray);
  font-size: 13px;
  font-weight: 500;
  font-family: var(--body-font);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}
.light .percentage-button {
  background: var(--light-weak-grey);
  border: 2px solid var(--light-border);
  color: #000;
}

.percentage-button:hover {
  background: var(--box-hover);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  border: solid 1px var(--primary-color);
}
.light .percentage-button:hover {
  background: #000;
  color: #fff;
  border: 2px solid var(--light-border);
}

.percentage-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Removed active state as requested */
.text-info-small {
  font-family: var(--body-font);
  font-size: 18px;
  font-weight: 500;
}

.settings-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 30px;
}

.settings-icon-container svg {
  width: 24px;
  height: 30px;
  color: var(--primary-color);
  vertical-align: bottom;
}

.settings-icon-container svg .stop1 {
  stop-color: var(--primary-color);
}

.settings-icon-container svg .stop2 {
  stop-color: var(--secondary-color);
}

.settings-options {
  display: flex;
  width: 100%;
  gap: 8px;
  justify-content: space-between;
  background: var(--box-inner);
  border-radius: 25px;
}

.option-button {
  width: 18%; /* 20% minus the gap */
  padding: 5px 0;
  background: transparent;
  border: solid 2px transparent;
  border-radius: 25px;
  color: var(--text-gray);
  font-size: 11px;
  font-weight: 500;
  font-family: var(--body-font);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-align: center;
  animation: fadeInScale 0.8s ease-out forwards;
  opacity: 0;
  transform: scale(0.9);
  animation-delay: calc(0.2s * var(--btn-index, 1));
  will-change: transform, opacity;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  40% {
    opacity: 0.6;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.option-button:nth-child(1) {
  --btn-index: 1;
}
.option-button:nth-child(2) {
  --btn-index: 2;
}
.option-button:nth-child(3) {
  --btn-index: 3;
}
.option-button:nth-child(4) {
  --btn-index: 4;
}
.option-button:nth-child(5) {
  --btn-index: 5;
}

.option-button:hover {
  background: #2a2a2a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.option-button.active {
  border: solid 2px var(--primary-color);
  font-weight: 600;
  box-shadow: 0 0 10px rgba(252, 224, 37, 0.3);
}

/* Custom input styles */
.custom-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;
  margin-bottom: 10px;
}

.custom-input {
  flex: 1;
  background: var(--box-inner);
  border: none;
  border-radius: 20px;
  color: var(--text);
  font-size: 18px;
  font-weight: 500;
  padding: 12px;
  text-align: center;
}

.custom-input:focus {
  outline: none;
}

.custom-input-button {
  background: transparent;
  border: solid 2px var(--primary-color);
  border-radius: 20px;
  color: var(--primary-color);
  font-size: 18px;
  font-weight: 600;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.custom-input-button:hover {
  background: #2a2a2a;
}
.active {
  transition: all 0.1s ease-in-out;
  color: var(--text);
  cursor: pointer;
}

.swapactive {
  box-shadow: var(--primary-color) 0px -2px 0px 0px inset;
}
.support-button {
  position: absolute;
  font-size: 0.9rem;
  font-weight: 500;
  top: 0px;
  right: 0px;
  border-bottom-left-radius: 15px;
  border-top-right-radius: 15px;
  color: var(--primary-color);
  background: #18191c;
  /*   box-shadow: rgb(49, 203, 158) 0px -2px 0px 0px inset;
 */
  padding: 10px;
  transform: all 0.3s ease-in-out;
}
.support-button:hover {
  background: var(--box-hover);
  cursor: pointer;
}
.pre-container {
  padding-top: 8px;
  padding-left: 8px;
  padding-right: 8px;
  padding-bottom: 8px;
  position: relative;
  width: 98%;
  border-radius: 25px;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  max-width: 450px;
  background: var(--box-bg);
}

.pre-container:hover {
  transform: scale(1.01);
}
.token-list-remove {
  position: absolute;
  width: 15px;
  height: 15px;
  right: -1px;
  top: 0px;
  color: var(--text);
  cursor: pointer;
  transition: all 0.1s ease-in-out;
  border-radius: 5px;
  z-index: 20;
  pointer-events: all;
}
.token-list-remove:hover {
  background-color: var(--box-inner);
  transform: scale(1.2);
}
.base-tokens-section {
  width: 100%;
}
.base-tokens {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}
.base-item {
  border: 1px solid rgba(255, 255, 255, 0.07);
  border-radius: 18px;
  display: flex;
  padding: 5px 12px 5px 6px;
  line-height: 0px;
  -webkit-box-align: center;
  align-items: center;
  justify-content: center;
  transition: all 0.1s ease-in-out;
}
.base-item:hover {
  background: var(--box-hover);
  cursor: pointer;
}

.base-item.disable {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

.tg-text {
  font-size: 0.7rem;
  font-weight: 500;
}
.right-section-container {
  display: none;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  width: 430px;
  height: 550px;
  border-radius: 25px;
  box-shadow: black 0px 0px 10px 0px;
  border: none;
  margin-inline: 5px;
  min-width: 250px;
  overflow: hidden;
}
.mid-section {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  height: 90%;
  width: 100%;
  overflow: hidden; /* Prevent content from being visible outside the container during animation */
  position: relative; /* Will be changed to absolute when hidden */
  z-index: 1; /* Ensure it's above other elements when visible */
  will-change: opacity, position; /* Optimize for animation */
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes chartSlideIn {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.iframe-container {
  margin-inline: 5px;
  width: 100%;
  height: 100%;
  border-radius: 25px;
  position: relative;
  display: flex;
  justify-content: flex-end;
  background-color: var(--box-darker);
  box-shadow: rgba(0, 0, 0, 0.24) 12px 16px 24px,
    rgba(0, 0, 0, 0.24) 12px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
  min-height: 300px; /* Ensure minimum height for the chart */
  will-change: transform; /* Optimize for animation */
}

/* Iframe styles */
.iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 15px;
  flex: 1;
  min-height: 300px;
}

.swap-container {
  position: relative;
  max-width: 500px;
  width: 100%;
  overflow: visible;
  border-radius: 45px;
  padding: 8px;
  box-shadow: rgba(0, 0, 0, 0.24) 12px 16px 24px,
    rgba(0, 0, 0, 0.24) 12px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
  background: var(--box-bg);
  margin-top: 35px;
  transition: all 0.3s ease-in-out;
}
.light .swap-container {
  background: var(--light-gray);
  border: 2px solid var(--light-border);
  box-shadow: none;
}
.left-section {
  width: 30%;
  min-width: 400px;
  padding-right: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.token-list-row-sb {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 4px;
}
.token-list-col {
  font-size: 1.2rem;

  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.token-list-col-right {
  font-size: 1.2rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  overflow: hidden;
}

.scale-switch {
  transform: scale(1.2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.general-box {
  padding: 15px;
  border-radius: 20px;
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  z-index: 4;
  overflow: visible;
  background: var(--box-inner);
}

.auto-container {
  width: 100%;
  margin-top: 2px;
  border-radius: 5px;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
}
.gwei-info {
  padding-inline-start: 7px;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-gray);
  border-radius: 12px;
  padding: 5px 10px;
}
.auto-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  background: var(--box-hover);
  color: var(--text);
  font-size: 0.8rem;
  text-align: center;
  height: 30px;
  font-weight: 500;
  outline: none;
  transition: all 0.1s ease-in-out;
  border: none;
  border-radius: 5px;
}
.auto-button:hover {
  outline: none;
  background-color: var(--box-hover);
  cursor: pointer;
}
.auto-button-non-active {
  cursor: pointer;
  border-radius: 5px;

  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  color: var(--text-gray);
  background-color: var(--box-darker);
  font-size: 0.8rem;
  text-align: center;
  height: 30px;
  font-weight: 500;
  outline: none;
  transition: all 0.1s ease-in-out;
  border: none;
}
.auto-button-non-active:hover {
  background-color: var(--box-hover);
  cursor: pointer;
}
.upper-settings {
  font-weight: 500;
  color: var(--text);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.us-te {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}

.gas-input-box {
  width: 100%;
  height: 30px;
  background: var(--box-inner);
  color: var(--text);
  font-size: 1rem;
  font-weight: 500;
  outline: none;
  text-align: center;
  border: 2px transparent;
  overflow: hidden;
  border-radius: 10px;
}
.white {
  color: var(--primary-color);
}
.input-hover {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 200px;
  border: solid 1px var(--border);
  border-radius: 10px;
}
.dev-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: fixed;
  bottom: 0;
  left: 0;

  align-items: center;
  width: fit-content;
  height: fit-content;
  border: solid 1px red;
  z-index: 1000;
}
.promo-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  overflow: hidden; /* Hide tokens outside of the visible area */
}

.promo-tokens-section {
  width: 80px;
}

.promo-tokens {
  display: flex;
  transition: transform 1s ease-in-out; /* Smooth transition for sliding effect */
}

.token-item {
  flex: 0 0 100%; /* Each token takes full width of the container */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 25px;
  transition: all 0.3s ease-in-out;
  opacity: 0.5;
}
.token-item:hover {
  cursor: pointer;
  transform: scale(0.95);
  background-color: var(--box-hover);
}

.token-logo {
  border-radius: 50%;
  object-fit: contain;
}
.token-active {
  opacity: 1;
}
.banner-ad-container {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  bottom: 0;
  right: 0;
}

.banner-ad {
  border-radius: 25px;
  transition: all 0.2s ease-in-out;
}

.banner-ad:hover {
  cursor: pointer;
  transform: scale(1.02);
}

.portfolio-list-buttons {
  display: flex;
  justify-content: center;
  flex-direction: row;
  gap: 10px;
  margin-top: 3px;
}
.portfolio-list-button {
  display: flex;
  justify-content: center;
  flex-direction: row;
  padding: 4px;
  width: 55px;
  font-size: 1rem;
  font-weight: 500;
  height: fit-content;
  border: solid 1px var(--primary-color);
  border-radius: 10px;
  transition: all 0.3s ease-in-out;
}
/* Removed empty ruleset */
.portfolio-list-button:hover {
  cursor: pointer;
  background: var(--primary-color);
}
.no-cursor {
  cursor: default;
}
.total-value {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 8px;
  font-size: 1.4rem;
  font-weight: 500;
  color: var(--primary-color);
  border-top-right-radius: 2px;
}

.block-timer-container {
  margin: 10px;
  padding: 10px;
  background-color: #f4f4f4;
  border-radius: 5px;
}

.last-block-time,
.estimated-time,
.trading-tip {
  margin-bottom: 4px;
}

.progress-bar-background {
  width: 100%;
  background-color: var(--box-inner);
  border-radius: 12px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 5px;
  background: var(--button-gradient);
  transition: width 0.1s ease-in-out;
}
.tooltip-container {
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  overflow: visible;
}

.tooltip-box {
  overflow: visible;
  position: relative;
  color: var(--text-gray);
  font-size: 0.9rem;
  width: 300px;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 5px;
  padding: 8px;
  padding: 14px;
  border: solid 1px var(--border);
  border-radius: 10px;
  background-color: var(--box-inner);
  text-align: center;
  z-index: 1000; /* Ensures the tooltip floats above other content */
  visibility: visible;
  opacity: 1;
  transition: all 0.3s ease-in-out;
}
.fa-info-circle {
  color: var(--text-gray);
}
.selector-tabs-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  align-items: center;
  width: 100%;
}
.selector-tabs-left {
  position: relative;
  display: flex;
  border: solid 1px var(--border);
  justify-content: center;
  border-radius: 25px;
  width: 100%;
  height: 45px;
  max-width: 600px;
}
.sub-tabs {
  position: relative;
  display: flex;
  border-bottom: solid 1px var(--border);
  justify-content: center;
  border-bottom-left-radius: 25px;
  border-bottom-right-radius: 25px;
  width: 100%;
  height: 25px;
  max-width: 300px;
  min-width: 200px;
  margin-bottom: 20px;
}
.sub-tab {
  position: relative;
  background-color: var(--box-bg);
  height: 100%;
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-left: none; /* Default border to "none" */
  border-right: none; /* Default border to "none" */
  font-weight: 500;
  font-size: 1rem;
  color: var(--text-gray);
}
.selector-tab {
  background-color: var(--box-bg);
  height: 100%;
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-left: none; /* Default border to "none" */
  border-right: none; /* Default border to "none" */
  font-weight: 500;
  font-size: 1.2rem;
  color: var(--text-gray);
}
.selector-tab:hover {
  cursor: pointer;
  background-color: var(--tab-hover);
}
.sub-tab:hover {
  cursor: pointer;
  background-color: var(--tab-hover);
}

/* Active tab style */
.tabactive {
  background-color: #18191c;
  color: var(--text);
}
.sub-tab-active {
  background-color: #18191c;
  color: var(--text);
}

/* Specific tab borders using :nth-child */
.selector-tabs-left .selector-tab:first-child {
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
}

.selector-tabs-left .selector-tab:last-child {
  border-top-right-radius: 25px;
  border-bottom-right-radius: 25px;
}

.sub-tabs .sub-tab:first-child {
  border-bottom-left-radius: 25px;
}

.sub-tabs .sub-tab:last-child {
  border-bottom-right-radius: 25px;
}
.loader-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loader {
  display: inline-block;
  position: relative;
  background: transparent;
  border-radius: 50%;
}

.loader:before,
.loader:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid transparent;
}

.loader:before {
  border-top-color: var(--primary-color);
  border-right-color: var(--primary-color);
  animation: spin 1s linear infinite;
}

.loader:after {
  border-bottom-color: var(--primary-color);
  border-left-color: var(--primary-color);
  animation: spin 1.5s linear reverse infinite;
}

/* Loader sizes */
.loader-small {
  width: 32px;
  height: 32px;
}

.loader-small:before,
.loader-small:after {
  border-width: 2px;
}

.loader-medium {
  width: 48px;
  height: 48px;
}

.loader-medium:before,
.loader-medium:after {
  border-width: 3px;
}

.loader-large {
  width: 64px;
  height: 64px;
}

.loader-large:before,
.loader-large:after {
  border-width: 3px;
}

/* Initial loader styling */
.initial-loader {
  transform: scale(2);
}

.initial-loader .loader {
  width: 80px;
  height: 80px;
}

.initial-loader .loader:before,
.initial-loader .loader:after {
  border-width: 4px;
  box-shadow: 0 0 20px rgba(252, 224, 37, 0.7);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* .loader {
  border: 8px solid var(--border);
  border-top: 8px solid var(--primary-color);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: auto; /* Center the spinner */

/* Tick icon styling moved to .status-icon and .tick-icon classes below */

.logo-image {
  margin-right: 50px;
  margin-left: 0;
  width: 140px;
  height: 50px;
  object-fit: contain;
}
button[data-testid="w3m-network-button"] wui-text {
  display: none !important;
}
.melt-desktop {
  display: flex;
  pointer-events: none;
  position: fixed;
  top: 0;
  left: 0;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.nav-buttons {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 30px;
  padding-left: 0;
  padding-top: 6px;
  height: 40px; /* Match the logo height */
  margin-top: 0;
}
.nav-button {
  font-size: 18px;
  transition: all 0.3s ease-in-out;
  color: var(--text-gray);
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 100%;
  border-radius: 8px;
}
.light .nav-button {
  color: #000;
}
.nav-button:hover {
  color: var(--text);
}

.nav-active {
  color: var(--primary-color);
}

.dropdown-menu-container {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  bottom: 0;
  height: 100vh;
  width: 100vw;
  z-index: 199;
  pointer-events: none;
}
.dropdown-menu {
  transition: all 0.3s ease-in-out;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  border: solid 1px var(--border);
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
  padding: 20px;
  z-index: 200;
  transform: translateY(100%);
  background: var(--box-darker);
}
.dropdown-open {
  transform: translateY(0%);
}
.dropdown-close {
  transform: translateY(100%);
}
.dropdown-item {
  z-index: 1001;
  pointer-events: all;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 10px;
  font-size: 1.2rem;
  border-radius: 8px;
  margin-bottom: 15px;
  font-weight: 500;
  color: var(--text-gray);
  border: solid 1px var(--border);
  background-color: var(--box-inner);
  transition: all 0.3s ease-in-out;
}
.dropdown-active {
  color: var(--text);
}
/* Global CSS to hide the text inside wui-network-button */

@keyframes fadeInContainer {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInBox {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.pending-container {
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: fixed;
  height: 100vh;
  width: 100vw;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  animation: fadeInContainer 0.3s ease-out forwards;
}

.pending-box {
  z-index: 10000;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: solid 1px var(--border);
  background: var(--box-bg);
  border-radius: 20px;
  width: 90%;
  max-width: 380px;
  pointer-events: all;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease-in-out;
  animation: fadeInBox 0.3s ease-out forwards;
}

.pending-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border);
  margin-bottom: 10px;
}

.pending-title {
  font-size: 0.9rem;
  font-weight: bold;
  color: var(--text);
  line-height: 1.2;
  margin-inline: 3px;
  text-align: center;
  font-weight: 500 !important;
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 1;
  font-size: 1.5rem;
  font-weight: 500;
  color: var(--text-gray);
  cursor: pointer;
  transition: color 0.2s ease;
  line-height: 1;
}

.close-button:hover {
  color: var(--text);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pending-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
  width: 100%;
  margin: 0;
  position: relative;
}

.pending-content > div {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 0.3s ease forwards;
}

.explorer-button {
  display: block;
  width: 100%;
  padding: 14px 20px;
  border-radius: 25px;
  background-color: rgba(0, 0, 0, 0.2);
  border: none;
  color: var(--primary-color);
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s ease;
}

.explorer-button:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

.pending-text {
  position: relative;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-gray);
  text-align: center;
  margin: 15px 0 20px 0;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: transform, opacity;
}

.pending-text-ok {
  position: relative;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text);
  text-align: center;
  margin: 0 0 20px 0;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  will-change: transform, opacity;
}

.status-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  background-color: transparent;
  border-radius: 50%;
  transition: all 0.3s ease;
}

/* Status icons styling is now handled directly in the SVG */

.dev-note {
  display: block;
  font-size: 0.8rem;
  color: #ff9900;
  margin-top: 5px;
  font-style: italic;
}

/* Swap Confirmation Styles */
@keyframes fadeInContent {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.confirmation-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 15px;
  animation: fadeInContent 0.4s ease-out forwards;
  animation-delay: 0.1s;
  opacity: 0;
}

.confirmation-amounts {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.confirmation-amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.confirmation-label {
  font-size: 0.9rem;
  color: var(--text-gray);
}

.confirmation-value {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.confirmation-amount {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.confirmation-token {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--text-gray);
  margin-top: 2px;
}

.confirmation-arrow {
  text-align: center;
  font-size: 1.2rem;
  color: var(--text-gray);
  margin: 5px 0;
}

.confirmation-actions {
  display: flex;
  gap: 10px;
  width: 100%;
  margin-top: 10px;
  animation: fadeInContent 0.4s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.cancel-button {
  flex: 1;
  padding: 12px;
  border-radius: 20px;
  background-color: var(--box-inner);
  border: 1px solid var(--border);
  color: var(--text);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: var(--box-hover);
}

.confirm-button {
  flex: 1;
  padding: 12px;
  border-radius: 20px;
  background-color: var(--primary-color);
  border: 1px solid rgba(255, 165, 0, 0.3);
  color: black;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.confirm-button:hover {
  background-color: var(--primary-color);
  filter: brightness(1.1);
}

.swap-fee-container {
  position: relative;
  opacity: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 300px;
  border-radius: 15px;
  background: var(--box-darker);
  padding: 15px;
  margin-left: 10px;
  transition: all 0.5s ease-in-out;
  /*   box-shadow: var(--box-shadow);
  background: var(--box-darker);
  border: solid 1px var(--border);
 */
}
.swap-fee-title {
  padding-inline-start: 5px;
  width: 100%;
  font-size: 0.9rem;
  color: var(--text);
  display: flex;
  justify-content: flex-start;
  margin-bottom: 5px;
}
.swap-fee-text {
  position: absolute;
  top: -10px;
  padding-inline-start: 0px;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-gray);
  background-color: var(--box-inner);

  padding: 2px 8px;
  border-radius: 5px;
  text-align: center;
}
.swap-fee-item {
  margin-top: 2px;
  margin-bottom: 2px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  padding: 5px 10px;
  background-color: var(--box-inner);
  border-radius: 10px;
}
.swap-fee-item:hover {
  background-color: var(--box-hover);
}
.swap-fee-box-left {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.swap-fee-box-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}
.swap-fee-amount {
  font-size: 1rem;
  color: var(--text);
  letter-spacing: 1px;
}
.swap-fee-item-name {
  font-size: 0.9rem;
  color: var(--text-gray);
}
.swap-fee-item-percentage {
  font-size: 1rem;
  color: var(--text-gray);
}
.Pineapple {
  font-size: 0.8rem;
  font-weight: 500;
  letter-spacing: 0.1rem;
  background: linear-gradient(
    to right,
    var(--primary-rgb),
    var(--secondary-rgb),
    var(--primary-rgb),
    var(--primary-rgb)
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: start;
  width: 100%;
  padding-bottom: 3px;
}

.listing-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 10px;
  border-radius: 15px;
}
.listing-url {
  position: absolute;
  top: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 2px 8px;
  border-radius: 10px;
  box-shadow: var(--primary-color) 0px 0px 5px 0px;
  opacity: 0.8;
  color: var(--text-gray);
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.1s ease-in-out;
  background: var(--box-darker);
}
.listing-url:hover {
  color: var(--text);
  opacity: 1;

  cursor: pointer;
}
.ad-container {
  position: fixed;
  bottom: 5vh;
  left: 0;
  width: fit-content;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 10px;
  padding: 16px;
  color: white;
  display: flex;
  flex-direction: column;
}

.ad-header {
  display: flex;
  align-items: center;
}

.ad-image {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  margin-right: 16px;
}

.ad-details h2 {
  margin: 0;
  font-size: 0.9rem;
}

.ad-details p {
  font-size: 18px;
  margin: 4px 0 0 0;
  max-width: 350px;
}

.ad-actions {
  gap: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
  border-radius: 10px;
}

.ad-button-chart {
  background-color: var(--box-inner);
  border-radius: 10px;
  text-align: center;
  width: 100%;
  border: none;
  color: white;
  padding: 8px 16px;
  text-decoration: none;
  cursor: pointer;
}
.ad-button-chart:hover {
  background-color: var(--box-hover);
  transition: all 0.1s ease-in-out;
}

.ad-button {
  background-color: var(--box-inner);
  border-radius: 10px;
  text-align: center;
  width: fit-content;
  border: none;
  color: white;
  padding: 8px;
  text-decoration: none;
  cursor: pointer;
}

.ad-button:hover {
  background-color: var(--box-hover);
  transition: all 0.1s ease-in-out;
}
.ad-close {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: var(--box-inner);
  border-radius: 5px;
  text-align: center;
  border: none;
  color: white;
  padding: 2px 6px;
  text-decoration: none;
  cursor: pointer;
}

.ad-close:hover {
  background-color: var(--box-hover);
  transition: all 0.1s ease-in-out;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media (max-width: 900px) {
}

/* Mobile Navbar Styles */
.mobile-navbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: var(--box-darker);
  border-top: 1px solid rgba(255, 255, 255, 0.07);
  padding: 10px 0;
  z-index: 100;
  box-shadow: rgba(0, 0, 0, 0.15) 0px -4px 12px;
  height: 70px;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  width: 33.33%; /* Changed from 25% to 33.33% for 3 items */
  color: var(--text-gray);
  font-size: 0.9rem;
  transition: all 0.2s ease;
  border-radius: 8px;
}

.mobile-nav-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.mobile-nav-item span {
  margin-top: 6px;
  font-weight: 500;
}

.mobile-nav-active {
  color: var(--primary-color);
}

.logo-image-mobile {
  height: 42px;
  margin-right: 0;
  margin-left: 0;
  object-fit: contain;
  max-width: 140px;
}

.mobile-mode-selector-fixed {
  position: absolute;
  top: 70px; /* Position below the navbar - updated to match new navbar height */
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 90;
  padding: 10px 0;
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  background-color: rgba(0, 0, 0, 0.3);
}

.mobile-tabs-row {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 5px 0;
}

.no-click {
  pointer-events: none;
}

@media (max-width: 600px) {
  .glider {
    border-radius: 10px;
  }
  /* Mobile-specific pending transaction styles */
  .pending-box {
    border: none;
    box-shadow: none;
    padding: 15px;
    width: 90%;
    max-width: 380px;
    border-radius: 10px;
  }
  .general-box {
    border-radius: 10px;
    padding: 15px;
    margin-top: 0px;
    margin-bottom: 0px;
  }
  /* Tab mode styles */
  /* Adjust tab mode container for mobile */
  .tab-mode-container {
    position: absolute;
    top: 11vh;
    left: 0;
    width: 100%;
    margin-right: 0;
    transform: scale(0.95);
    box-shadow: rgba(0, 0, 0, 0.15) 0px 4px 12px;
    border-radius: 10px;
  }

  .tabs-group {
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }

  /* Add top margin to main container to make room for the mode selector */
  .main-container {
    margin-top: 4vh !important;
  }
  .cere-dropdown-header {
    font-size: 0.9rem;
    min-width: auto;
    width: fit-content;
  }
  .ad-container {
    height: fit-content;
    position: fixed;
    z-index: 190;
    bottom: 70px; /* Adjusted to make room for the mobile navbar */
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 1);
    border-radius: 10px;
    padding: 16px;
    margin: 0;
    width: 100%;
    color: white;
    display: flex;
    flex-direction: column;
  }
  .swap-fee-container {
    padding: 5px;
    width: 95%;
    margin-bottom: 80px; /* Adjusted to make room for the mobile navbar */
    margin-left: 0px;
  }

  .basic-button {
    font-size: 18px;
    margin-right: 2vw;
  }
  .listing-container {
    height: 80vh;
    box-shadow: none;
  }
  -content {
    font-size: 0.8rem;
  }
  .right-section-container {
    width: 100%;
    height: 65vh;
  }
  .iframe-container {
    min-width: 90%;
  }
  .footer-left {
    font-size: 18px;
  }
  .mid-section {
    width: 95%;
    height: 50vh;
    margin-top: 0px;
    margin-bottom: 20px; /* Adjusted to make room for the mobile navbar */
    padding-left: 0px;
    padding-right: 0px;
  }
  .swap-upper {
    padding: 10px;
  }
  .mempool-container {
    overflow: scroll;
  }
  .gas-price-box {
    width: 100%;
  }
  .us-ts {
    font-size: 0.8rem;
  }
  .us-te {
    font-size: 0.8rem;
    width: 40%;
  }
  .swap-container {
    background: none;
    padding: 0;
    min-width: 50px;
    width: 95%;
    margin-bottom: 0px; /* Adjusted to make room for the mobile navbar */
  }
  .token-list-container {
    width: 100%;
  }
  .token-list {
    position: fixed;
    margin: 0;
    bottom: 70px; /* Adjusted to make room for the mobile navbar */
    width: 100%;
    height: 80vh;
  }
  .tg-text {
    font-size: 0.7rem;
  }
  .base-symbol {
    font-size: 1rem;
  }

  .support-button {
    text-align: center;
  }
  .maintenance-container {
    display: flex;
    position: fixed;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100vw;
    background-color: var(--box-bg);
    opacity: 0.95;
    transition: all 4s ease-in-out;
    pointer-events: none;
    background-size: cover;
    z-index: 1000;
  }
  .warning-text {
    color: var(--text);
    font-size: 1.9rem;
    font-weight: 600;
    pointer-events: none;
  }

  .intro-box {
    font-size: 0.8rem;
  }
  .twitter-container {
    width: auto;
    height: auto;
  }
  .banner-container {
    height: 100px;
  }
  .quick-import {
    position: absolute;
    right: 0px;
    top: 8vh;
    width: 60vw;
    font-size: 0.7rem;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 0;
  }
  .gas-box-top {
    position: absolute;
    font-size: 0.8rem;
    left: 0px;
    top: 6vh;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-left-radius: 0px;
  }
  .nav-left {
    width: fit-content;
  }
  .nav-right {
    padding-inline: 0px;
    padding-top: 5px;
    width: fit-content;
    gap: 8px;
  }
  .wallet-button {
    font-size: 0.9rem;
  }

  .wallet-dropdown-content {
    width: 120px;
  }

  .wallet-dropdown-item {
    padding: 10px 14px;
    font-size: 14px;
  }
  .page-container {
    padding-top: 5px;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 90vh;
    width: 100%;
    color: white;
    padding-bottom: 70px; /* Adjusted to make room for the mobile navbar */
  }
  .connect-button {
    font-size: 1rem;
    height: 32px;
    margin-top: 5px;
  }
  .main-container {
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    margin-top: 0px;
    margin-bottom: 0px; /* Adjusted to make room for the mobile navbar */
    width: 100vw;
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
  }
  .hide {
    display: none;
  }
  .mobhide {
    display: none;
  }
  .nav-container {
    position: relative;
    height: 10vh;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0px;
    padding: 10px 15px;
    padding-right: 10px !important;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    box-shadow: none;
  }

  .mobile-nav-container {
    height: auto;
    flex-wrap: wrap;
  }

  .mobile-nav-container .nav-left {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
    justify-content: space-between;
  }

  .mobile-nav-container .nav-right {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    flex-direction: row;
  }
  .whole-container {
    min-width: none;
    min-height: 100vh;
    height: fit-content;
    width: 100vw;
    overflow-y: scroll;
    justify-content: flex-start;
    padding-bottom: 70px; /* Adjusted to make room for the mobile navbar */
  }
  .base-tokens-section {
    padding-top: 5px;
    padding-bottom: 5px;
  }
  .footer-container {
    display: none;
    padding-inline: 5px;
    bottom: 70px; /* Adjusted to make room for the mobile navbar */
  }
  .icon {
    width: 24px;
    height: 24px;
  }

  /* Hide the old dropdown menu */
  .dropdown-menu-container {
    display: none;
  }
}
