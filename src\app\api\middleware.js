import { NextResponse } from "next/server";
import jwt from "jsonwebtoken";
import { rateLimit } from "express-rate-limit";

const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error("JWT_SECRET is not defined in environment variables");
}

const authCache = new Map();

const CACHE_EXPIRATION = 5 * 60 * 1000;

const limiter = {
  windowMs: 15 * 60 * 1000,
  max: 100,
  standardHeaders: true,
  legacyHeaders: false,
  store: new Map(),
};

export async function verifyToken(request) {
  try {
    if (!JWT_SECRET) {
      console.error("JWT_SECRET is not defined, authentication is disabled");
      return {
        isValid: false,
        error: "Authentication is disabled (JWT_SECRET not configured)",
      };
    }

    const authHeader = request.headers.get("authorization");

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      console.log("Missing or invalid authorization header");
      return {
        isValid: false,
        error: "Missing or invalid authorization header",
      };
    }

    const token = authHeader.split(" ")[1];

    if (!token) {
      console.log("Token is required");
      return { isValid: false, error: "Token is required" };
    }

    if (authCache.has(token)) {
      const cachedAuth = authCache.get(token);
      const now = Date.now();

      if (now - cachedAuth.timestamp < CACHE_EXPIRATION) {
        if (cachedAuth.result.isValid) {
          return cachedAuth.result;
        } else {
          console.log("Using cached invalid token result");
          return cachedAuth.result;
        }
      }

      authCache.delete(token);
    }

    const decoded = jwt.verify(token, JWT_SECRET);
    console.log("Token verified successfully for address:", decoded.address);

    const result = { isValid: true, address: decoded.address };

    authCache.set(token, {
      timestamp: Date.now(),
      result,
    });

    return result;
  } catch (error) {
    console.warn("Token verification error:", error);
    const result = { isValid: false, error: "Invalid token" };

    if (error.name !== "TokenExpiredError") {
      try {
        const token = request.headers.get("authorization")?.split(" ")[1];
        if (token) {
          authCache.set(token, {
            timestamp: Date.now(),
            result,
          });
        }
      } catch (cacheError) {
        console.warn("Error caching token result:", cacheError);
      }
    }

    return result;
  }
}

export async function checkRateLimit(request) {
  const ip = request.headers.get("x-forwarded-for") || "unknown";

  if (!limiter.store.has(ip)) {
    limiter.store.set(ip, {
      count: 0,
      resetTime: Date.now() + limiter.windowMs,
    });
  }

  const client = limiter.store.get(ip);

  if (Date.now() > client.resetTime) {
    client.count = 0;
    client.resetTime = Date.now() + limiter.windowMs;
  }

  client.count++;

  if (client.count > limiter.max) {
    return {
      isLimited: true,
      error: "Too many requests, please try again later",
    };
  }

  return { isLimited: false };
}

const rpcAuthCache = new Map();

function getRequestId(request) {
  try {
    const url = request.url || "";
    const method = request.method || "";
    const authHeader = request.headers.get("authorization") || "";

    return `${method}-${url}-${authHeader}`;
  } catch (error) {
    console.warn("Error generating request ID:", error);
    return Date.now().toString();
  }
}

export async function rpcAuthMiddleware(request) {
  const requestId = getRequestId(request);

  if (rpcAuthCache.has(requestId)) {
    const cachedAuth = rpcAuthCache.get(requestId);
    const now = Date.now();

    if (now - cachedAuth.timestamp < CACHE_EXPIRATION) {
      return cachedAuth.result;
    }

    rpcAuthCache.delete(requestId);
  }

  const rateLimitCheck = await checkRateLimit(request);
  if (rateLimitCheck.isLimited) {
    console.log("Rate limit exceeded:", rateLimitCheck.error);
    const result = {
      success: false,
      response: NextResponse.json(
        { error: rateLimitCheck.error },
        { status: 429 }
      ),
    };

    rpcAuthCache.set(requestId, {
      timestamp: Date.now(),
      result,
    });

    return result;
  }

  const tokenCheck = await verifyToken(request);
  if (!tokenCheck.isValid) {
    console.log("Token verification failed:", tokenCheck.error);
    const result = {
      success: false,
      response: NextResponse.json({ error: tokenCheck.error }, { status: 401 }),
    };

    rpcAuthCache.set(requestId, {
      timestamp: Date.now(),
      result,
    });

    return result;
  }

  const result = {
    success: true,
    address: tokenCheck.address,
    headers: { "X-Authenticated-Address": tokenCheck.address },
  };

  rpcAuthCache.set(requestId, {
    timestamp: Date.now(),
    result,
  });

  return result;
}
