/* import {BlockchainContext} from './BlockchainContext';
import {useState, useEffect, useContext, useRef, useMemo, use} from 'react';

function TokenList({
  type,
  handleSellTokenChange,
  handleBuyTokenChange,
  handleShowTokenList,
  buyToken,
  sellToken,
  handleContractImport,
}) {
  const {dollarRef, account, provider, chain_id, ALL_TOKENS} =
    useContext(BlockchainContext);
  const [tokens, setTokens] = useState(ALL_TOKENS);
  const [newBlock, setNewBlock] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [fadeIn, setFadeIn] = useState(false);

  function handleSearchAndImport(value) {
    setSearchTerm(value);
    if (value.length === 42) {
      setTimeout(() => {
        handleContractImport(value);
      }, 500);
    }
  }

  const [dollarRefTrigger, setDollarRefTrigger] = useState(0);

  const previousDollarRef = useRef();

  useEffect(() => {
    const checkNewDollarRef = () => {
      if (previousDollarRef.current !== dollarRef.current) {

        setDollarRefTrigger((prev) => prev + 1);
        previousDollarRef.current = dollarRef.current;
      }
    };

    const intervalId = setInterval(checkNewDollarRef, 500);

    return () => {
      clearInterval(intervalId);
    };
  }, [dollarRef]);
  const tokenListRef = useRef(null);


  useEffect(() => {
    const handleClickOutside = (event) => {




      if (
        tokenListRef.current &&
        !tokenListRef.current.contains(event.target)
      ) {
        handleShowTokenList(false);

      }
    };


    document.addEventListener('mousedown', handleClickOutside);


    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    let isLoading = false;
    try {
      if (dollarRef.current && account) {
        if (isLoading) {
          console.log('Loading in progress, skipping...');
          return;
        }


        isLoading = true;


        const tokenEntries = Object.entries(dollarRef.current);


        const firstFourTokens = tokenEntries.slice(0, 1);

        const remainingTokens = tokenEntries.slice(1).sort((a, b) => {
          return Number(b[1].dollarValue) - Number(a[1].dollarValue);
        });



        const sortedTokens = [...firstFourTokens, ...remainingTokens].reduce(
          (acc, [key, value]) => {
            acc[key] = value;

            return acc;
          },
          {}
        );


        setTokens(sortedTokens);
      } else {

      }
    } catch (error) {

    } finally {
      isLoading = false;

    }
  }, [account, dollarRef, chain_id, dollarRefTrigger]);

  useEffect(() => {
    setFadeIn(true);
  }, []);

  const filteredAndSortedTokens = useMemo(() => {
    return Object.entries(tokens)
      .filter(
        ([key, token]) =>
          token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          token.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort((a, b) => Number(b[1].dollarValue) - Number(a[1].dollarValue));
  }, [tokens, searchTerm]);

  return (
    <div className={`token-list-container ${fadeIn ? 'fade-in' : ''}`}>
      <div className='token-list' ref={tokenListRef}>
        <div className='token-list-top'>
          <div className='inputWithIcon'>
            <svg
              className='inputIcon'
              stroke='currentColor'
              fill='currentColor'
              strokeWidth='0'
              viewBox='0 0 24 24'
              height='1.4rem'
              width='1.4rem'
              xmlns='http:
              <path
                fillRule='evenodd'
                d='M21.53 20.47L17.689 16.629C18.973 15.106 19.75 13.143 19.75 11C19.75 6.175 15.825 2.25 11 2.25C6.175 2.25 2.25 6.175 2.25 11C2.25 15.825 6.175 19.75 11 19.75C13.143 19.75 15.106 18.973 16.629 17.689L20.47 21.53C20.616 21.676 20.808 21.75 21 21.75C21.192 21.75 21.384 21.677 21.53 21.53C21.823 21.238 21.823 20.763 21.53 20.47ZM3.75 11C3.75 7.002 7.002 3.75 11 3.75C14.998 3.75 18.25 7.002 18.25 11C18.25 14.998 14.998 18.25 11 18.25C7.002 18.25 3.75 14.998 3.75 11Z'
              />
            </svg>
            <input
              className='quick-import-bar'
              type='text'
              placeholder='Search name or paste contract'
              value={searchTerm}
              onChange={(e) => handleSearchAndImport(e.target.value)}
            />
          </div>
          <div className='base-tokens-section'>
            <div className='base-tokens'>
              {Object.entries(tokens)
                .filter(([key, token]) => token.is_partner)
                .map(([key, token]) => (
                  <div
                    className={`base-item ${token.is_partner ? 'partner' : ''}`}
                    key={key}
                    onClick={() => {

                      if (type === 'sellToken') {
                        handleSellTokenChange(key);
                      } else if (type === 'buyToken') {
                        handleBuyTokenChange(key);
                      }
                      handleShowTokenList(false);
                    }}>
                    <img
                      src={token.logo_uri}
                      alt={token.name}
                      className='token-logo'
                      width={25}
                      height={25}
                      style={{
                        objectFit: 'contain',
                        borderRadius: '50%',
                        marginRight: '2px',
                      }}
                    />
                    <div className='base-symbol'>{token.symbol}</div>
                  </div>
                ))}
            </div>
          </div>
        </div>
        <div className='token-list-items'>
          {filteredAndSortedTokens.map(([key, token]) => (
            <div
              className={`token-list-item ${key} ${
                buyToken === key || sellToken === key ? 'disable' : ''
              }`}
              key={key}
              onClick={() => {

                if (type === 'sellToken') {
                  handleSellTokenChange(key);
                } else if (type === 'buyToken') {
                  handleBuyTokenChange(key);
                }
                handleShowTokenList(false);
              }}>
              <div className='token-list-item-image'>
                <img
                  src={token.logo_uri}
                  alt={'logo'}
                  width={36}
                  height={36}
                  style={{objectFit: 'contain', borderRadius: '50%'}}
                  loading='lazy'
                />
              </div>
              <div className='token-list-row-sb'>
                <div className='token-list-col'>
                  <div className='token-list-item-text-name'>
                    {token.symbol}
                  </div>
                  <div className='token-list-item-text-symbol'>
                    {token.name}
                  </div>
                </div>
                <div className='token-list-col-right'>
                  <div className='token-list-item-text-name'>
                    {account && (
                      <div className='token-list-item-text-name'>
                        {token.dollarValue === undefined ||
                        token.dollarValue === null ||
                        token.dollarValue === ''
                          ? ''
                          : '$' + token.dollarValue}
                      </div>
                    )}
                  </div>{' '}
                  <div className='token-list-item-text-symbol'>
                    {token.balance === undefined ? '' : token.balance}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
export default TokenList;
 */

import { useState, useEffect, useContext, useRef, useMemo } from "react";
import { BlockchainContext } from "../../context/BlockchainContext";
import { ETH_TOKENS_DISPLAY, BASE_TOKENS } from "../../constants/constants";
import Loader from "../common/Loader";
import { toast } from "react-hot-toast";

function TokenList({
  type,
  handleSellTokenChange,
  handleBuyTokenChange,
  handleShowTokenList,
  buyToken,
  sellToken,
  handleContractImport,
}) {
  const { chain_id, dollarRef, account } = useContext(BlockchainContext);
  const [tokens, setTokens] = useState({});
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [fadeIn, setFadeIn] = useState(false);
  const tokenListRef = useRef(null);

  const handleSearchAndImport = (value) => {
    toast("Database offline");
    return;
    setSearchTerm(value);
    if (value.length === 42) {
      setTimeout(() => {
        handleContractImport(value);
      }, 500);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        tokenListRef.current &&
        !tokenListRef.current.contains(event.target)
      ) {
        handleShowTokenList(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [tokenListRef]);

  const getHardcodedTokens = (chainId) => {
    if (chainId === 8453) {
      console.log(`TokenList: Using BASE_TOKENS for chain_id ${chainId}`);

      const baseTokens = {};
      Object.entries(BASE_TOKENS).forEach(([key, token]) => {
        if (token.chain_id === 8453) {
          baseTokens[key] = { ...token };
        }
      });

      console.log(
        `TokenList: Found ${
          Object.keys(baseTokens).length
        } BASE tokens for chain_id ${chainId}`
      );

      if (Object.keys(baseTokens).length === 0) {
        console.warn(
          `WARNING: No tokens found in BASE_TOKENS for chain_id ${chainId}`
        );
      }

      return baseTokens;
    } else if (chainId === 1) {
      console.log(
        `TokenList: Using ETH_TOKENS_DISPLAY for chain_id ${chainId}`
      );

      const ethTokens = {};
      Object.entries(ETH_TOKENS_DISPLAY).forEach(([key, token]) => {
        if (token.chain_id === 1) {
          ethTokens[key] = { ...token };
        }
      });

      console.log(
        `TokenList: Found ${
          Object.keys(ethTokens).length
        } ETH tokens for chain_id ${chainId}`
      );

      if (Object.keys(ethTokens).length === 0) {
        console.warn(
          `WARNING: No tokens found in ETH_TOKENS_DISPLAY for chain_id ${chainId}`
        );
      }

      return ethTokens;
    } else {
      console.log(
        `TokenList: No predefined tokens for chain_id ${chainId}, using empty list`
      );
      return {};
    }
  };

  useEffect(() => {
    console.log(`TokenList: Setting up tokens for chain_id ${chain_id}`);

    try {
      const hardcodedTokens = getHardcodedTokens(chain_id);

      console.log(
        `TokenList: Setting ${
          Object.keys(hardcodedTokens).length
        } tokens for chain_id ${chain_id}`
      );
      Object.entries(hardcodedTokens).forEach(([key, token]) => {
        console.log(
          `TokenList: Token ${key}: ${token.symbol} (${token.chain_id})`
        );
      });

      setTokens(hardcodedTokens);
      setLoading(false);
    } catch (error) {
      console.warn("Error setting up hardcoded tokens:", error);
      setLoading(false);
    }

    console.log("Account status:", account ? "connected" : "disconnected");
  }, [chain_id, account]);

  useEffect(() => {
    const setupTokens = async () => {
      try {
        console.log(
          `TokenList: Setting up tokens with dollarRef for chain_id ${chain_id}`
        );

        const initialTokens = getHardcodedTokens(chain_id);

        setTokens(initialTokens);
        setLoading(false);

        if (
          typeof account !== "undefined" &&
          account &&
          dollarRef?.current &&
          Object.keys(dollarRef.current).length > 0
        ) {
          const filteredTokens = {};
          Object.entries(dollarRef.current).forEach(([key, token]) => {
            if (token.chain_id === chain_id) {
              filteredTokens[key] = token;
            }
          });

          console.log(
            `Using dollarRef tokens for chain_id ${chain_id}, found ${
              Object.keys(filteredTokens).length
            } tokens`
          );

          if (Object.keys(filteredTokens).length > 0) {
            setTokens(filteredTokens);
          } else {
            console.log(
              `No tokens found in dollarRef for chain_id ${chain_id}, using hardcoded tokens`
            );
          }
        }
      } catch (error) {
        console.warn("Error setting up tokens:", error);
        setLoading(false);
      }
    };

    setupTokens();
  }, [chain_id, dollarRef, account]);

  useEffect(() => {
    setFadeIn(true);
  }, []);

  const filteredAndSortedTokens = useMemo(() => {
    return Object.entries(tokens)
      .filter(
        ([, token]) =>
          token?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          token?.symbol?.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .sort(
        (a, b) =>
          Number(b[1]?.dollarValue || 0) - Number(a[1]?.dollarValue || 0)
      );
  }, [tokens, searchTerm]);

  if (loading || Object.keys(tokens).length === 0) {
    return (
      <div className={`token-list-container ${fadeIn ? "fade-in" : ""}`}>
        <div className="token-list" ref={tokenListRef}>
          <Loader size="medium" />{" "}
        </div>
      </div>
    );
  }

  return (
    <div className={`token-list-container ${fadeIn ? "fade-in" : ""}`}>
      <div className="token-list" ref={tokenListRef}>
        <div className="token-list-top">
          {/* Header with title and close button */}
          {/*       <div className="pending-header">
            <div className="pending-title">Select a token</div>
            <div
              className="close-button"
              onClick={() => handleShowTokenList(false)}
            >
              ×
            </div>
          </div> */}

          {/* Search input */}
          <div className="inputWithIcon">
            <svg
              className="inputIcon"
              stroke="currentColor"
              fill="currentColor"
              strokeWidth="0"
              viewBox="0 0 24 24"
              height="1.4rem"
              width="1.4rem"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M21.53 20.47L17.689 16.629C18.973 15.106 19.75 13.143 19.75 11C19.75 6.175 15.825 2.25 11 2.25C6.175 2.25 2.25 6.175 2.25 11C2.25 15.825 6.175 19.75 11 19.75C13.143 19.75 15.106 18.973 16.629 17.689L20.47 21.53C20.616 21.676 20.808 21.75 21 21.75C21.192 21.75 21.384 21.677 21.53 21.53C21.823 21.238 21.823 20.763 21.53 20.47ZM3.75 11C3.75 7.002 7.002 3.75 11 3.75C14.998 3.75 18.25 7.002 18.25 11C18.25 14.998 14.998 18.25 11 18.25C7.002 18.25 3.75 14.998 3.75 11Z"
              />
            </svg>
            <input
              className="quick-import-bar"
              type="text"
              placeholder="Search name or paste contract"
              value={searchTerm}
              onChange={(e) => handleSearchAndImport(e.target.value)}
            />
          </div>

          <div className="base-tokens-section">
            <div className="base-tokens">
              {Object.entries(tokens)
                .filter(([, token]) => token?.is_partner)
                .map(([key, token]) => (
                  <div
                    className={`base-item ${
                      token?.is_partner ? "partner" : ""
                    } ${
                      buyToken === key || sellToken === key ? "disable" : ""
                    }`}
                    key={key}
                    onClick={() => {
                      if (type === "sellToken") handleSellTokenChange(key);
                      if (type === "buyToken") handleBuyTokenChange(key);
                      handleShowTokenList(false);
                    }}
                  >
                    <img
                      src={token?.logo_uri || "/default-image.png"}
                      alt={token?.name || "token"}
                      className="token-logo"
                      width={25}
                      height={25}
                      style={{
                        objectFit: "contain",
                        borderRadius: "50%",
                        marginRight: "2px",
                      }}
                    />
                    <div className="base-symbol">{token?.symbol || ""}</div>
                  </div>
                ))}
            </div>
          </div>
        </div>
        <div className="token-list-items">
          {filteredAndSortedTokens.map(([key, token]) => (
            <div
              className={`token-list-item ${key} ${
                buyToken === key || sellToken === key ? "disable" : ""
              }`}
              key={key}
              onClick={() => {
                if (type === "sellToken") handleSellTokenChange(key);
                if (type === "buyToken") handleBuyTokenChange(key);
                handleShowTokenList(false);
              }}
            >
              <div className="token-list-item-image">
                <img
                  src={token?.logo_uri || "/default-image.png"}
                  alt={token?.name || "token"}
                  width={36}
                  height={36}
                  style={{ objectFit: "contain", borderRadius: "50%" }}
                  loading="lazy"
                />
              </div>
              <div className="token-list-row-sb">
                <div className="token-list-col">
                  <div className="token-list-item-text-name">
                    {token?.symbol || ""}
                  </div>
                  <div className="token-list-item-text-symbol">
                    {token?.name || ""}
                  </div>
                </div>
                <div className="token-list-col-right">
                  <div className="token-list-item-text-name">
                    {token?.dollarValue ? `$${token.dollarValue}` : ""}
                  </div>
                  <div className="token-list-item-text-symbol">
                    {token?.balance || ""}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default TokenList;
