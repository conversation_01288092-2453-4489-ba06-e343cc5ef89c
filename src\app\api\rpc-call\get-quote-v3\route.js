import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";
import QuoterABI from "../../../../constants/abis/QuoterABI.json";
import { CHAINS } from "../../../../constants/constants";

// RPC URLs for different chains
const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }

    const authenticatedAddress = authResult.address;

    const body = await request.json();
    const { tokenIn, tokenOut, parsedSellAmount, chain_id } = body;

    if (
      !chain_id ||
      !CHAINS[chain_id] ||
      !tokenIn ||
      !tokenOut ||
      !parsedSellAmount
    ) {
      return NextResponse.json(
        { error: "Invalid or missing parameters" },
        { status: 400 }
      );
    }

    const provider = new ethers.JsonRpcProvider(RPC_URLS[chain_id]);
    const quoter = new ethers.Contract(
      CHAINS[chain_id].uniswapQuoterV3,
      QuoterABI,
      provider
    );

    const feeTiers = [500, 3000, 10000];
    let highestQuote = null;

    if (
      tokenIn !== CHAINS[chain_id].wethAddress &&
      tokenOut !== CHAINS[chain_id].wethAddress
    ) {
      let intermediateWethAmount = null;
      let feeIn = 0;

      for (const fee of feeTiers) {
        try {
          const amountOut = await quoter.quoteExactInputSingle.staticCall({
            tokenIn,
            tokenOut: CHAINS[chain_id].wethAddress,
            fee,
            amountIn: BigInt(parsedSellAmount),
            sqrtPriceLimitX96: 0,
          });

          if (
            !intermediateWethAmount ||
            amountOut[0] > intermediateWethAmount
          ) {
            intermediateWethAmount = amountOut[0];
            feeIn = fee;
          }
        } catch (error) {}
      }

      if (intermediateWethAmount) {
        for (const fee of feeTiers) {
          try {
            const amountOut = await quoter.quoteExactInputSingle.staticCall({
              tokenIn: CHAINS[chain_id].wethAddress,
              tokenOut,
              fee,
              amountIn: intermediateWethAmount,
              sqrtPriceLimitX96: 0,
            });

            if (!highestQuote || amountOut[0] > highestQuote.amountOut) {
              highestQuote = {
                amountOut: amountOut[0].toString(),
                feeIn,
                feeOut: fee,
              };
            }
          } catch (error) {}
        }
      } else {
        console.log("Failed to get an intermediate WETH quote");
        return NextResponse.json(
          { error: "Failed to get intermediate WETH quote" },
          { status: 500 }
        );
      }
    } else {
      for (const fee of feeTiers) {
        try {
          const amountOut = await quoter.quoteExactInputSingle.staticCall({
            tokenIn,
            tokenOut,
            fee,
            amountIn: BigInt(parsedSellAmount),
            sqrtPriceLimitX96: 0,
          });

          if (!highestQuote || amountOut[0] > highestQuote.amountOut) {
            highestQuote = {
              amountOut: amountOut[0].toString(),
              fee,
            };
          }
        } catch (error) {}
      }
    }

    if (highestQuote) {
      console.log("Highest V3 quote found:", highestQuote);
      return NextResponse.json({ highestQuote });
    } else {
      return NextResponse.json(
        { error: "No valid quote found" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.warn("Error fetching Uniswap V3 quote:", error);
    return NextResponse.json({ error: "Failed to get quote" }, { status: 500 });
  }
}
