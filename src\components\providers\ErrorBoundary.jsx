"use client";

import React from "react";
import { toast } from "react-hot-toast";

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };

    if (typeof window !== "undefined") {
      window.addEventListener("error", this.handleGlobalError);

      window.addEventListener(
        "unhandledrejection",
        this.handlePromiseRejection
      );

      this.originalConsoleError = console.error;
      console.error = (...args) => {
        this.originalConsoleError(...args);

        const errorString = args.join(" ");
        if (
          !errorString.includes(
            "React will try to recreate this component tree"
          )
        ) {
          this.handleConsoleError(args[0]);
        }
      };
    }
  }

  componentWillUnmount() {
    if (typeof window !== "undefined") {
      window.removeEventListener("error", this.handleGlobalError);
      window.removeEventListener(
        "unhandledrejection",
        this.handlePromiseRejection
      );
      console.error = this.originalConsoleError;
    }
  }

  handleGlobalError = (event) => {
    console.warn("Global error caught:", event.error);

    event.preventDefault();

    return true;
  };

  handlePromiseRejection = (event) => {
    console.warn("Unhandled promise rejection:", event.reason);
  };

  handleConsoleError = (error) => {};

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.warn("Error caught by ErrorBoundary:", error, errorInfo);

    this.setState({ errorInfo });
  }

  render() {
    if (this.state.hasError) {
      console.warn("Error caught by ErrorBoundary:", this.state.error);

      try {
        if (this.props.fallback) {
          return this.props.fallback;
        }

        if (process.env.NODE_ENV === "production") {
          try {
            return (
              <div className="error-recovery-wrapper">
                {/* We're intentionally not rendering the exact children that caused the error */}
                {/* Instead, we render a simplified version of the app structure */}
                <div
                  className="app-header-placeholder"
                  style={{ height: "60px" }}
                ></div>
                <div
                  className="app-content-placeholder"
                  style={{ padding: "20px" }}
                >
                  <div style={{ textAlign: "center", marginTop: "20px" }}>
                    <p>Some features may be temporarily unavailable.</p>
                  </div>
                </div>
              </div>
            );
          } catch (secondaryError) {
            console.warn("Failed to render recovery UI:", secondaryError);
          }
        }

        return (
          <div
            className="error-boundary-fallback"
            style={{
              width: "100%",
              height: "100%",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              padding: "20px",
            }}
          >
            {/* Empty container to maintain layout */}
          </div>
        );
      } catch (renderError) {
        console.warn("Error rendering fallback UI:", renderError);

        return <div style={{ display: "none" }}></div>;
      }
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
