import React, { useState, useEffect, useMemo } from "react";
import SwapConfirmation from "../swap/SwapConfirmation";
import PendingTransaction from "../wallet/PendingTransaction";

const styles = {
  container: {
    position: "fixed",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    zIndex: 9999,
    backdropFilter: "blur(5px)",
    transition: "opacity 0.2s ease-in-out",
  },
};

/**
 * TransactionFlow component that handles the transition between SwapConfirmation and PendingTransaction
 */
function TransactionFlow({
  isOpen,
  onClose,
  onConfirm,
  swapData,
  savedSlippageValue,
  savedAddedPriority,
  sellToken,
  buyToken,
  ALL_TOKENS,
  provider,
  chainId,
  pendingTxStatus,
  transaction,
  inSwap,
}) {
  const [activeView, setActiveView] = useState("confirmation");
  const [isClosing, setIsClosing] = useState(false);

  useEffect(() => {
    console.log("TransactionFlow mounted");
    return () => {
      console.log("TransactionFlow unmounted");
    };
  }, []);

  useEffect(() => {
    if (isOpen) {
      console.log("TransactionFlow opened, setting to confirmation view");
      setActiveView("confirmation");
    }
  }, [isOpen]);

  useEffect(() => {
    console.log("TransactionFlow - pendingTxStatus:", pendingTxStatus);
    console.log("TransactionFlow - transaction:", transaction);

    if (activeView !== "pending") {
      if (transaction) {
        console.log("Setting activeView to pending based on transaction");
        setActiveView("pending");
      } else if (
        pendingTxStatus === "waiting" ||
        pendingTxStatus === "pending"
      ) {
        console.log("Setting activeView to pending based on pendingTxStatus");
        setActiveView("pending");
      }
    }
  }, [transaction, pendingTxStatus, activeView]);

  const handleConfirm = () => {
    console.log("SwapConfirmation: User clicked Confirm Swap button");

    setActiveView("pending");

    onConfirm();
  };

  const handleClose = () => {
    console.log("Closing TransactionFlow");
    setIsClosing(true);

    onClose();
  };

  if (!isOpen) {
    return null;
  }

  console.log("TransactionFlow rendering with:", {
    activeView,
    isOpen,
    swapData: swapData ? "present" : "missing",
    pendingTxStatus,
    transaction: transaction ? "present" : "missing",
  });

  const memoizedConfirmation = useMemo(() => {
    console.log("Memoizing SwapConfirmation component");
    console.log(
      "Passing savedAddedPriority to SwapConfirmation:",
      savedAddedPriority
    );

    return (
      <SwapConfirmation
        onConfirm={handleConfirm}
        onCancel={handleClose}
        swapData={swapData}
        savedSlippageValue={savedSlippageValue}
        savedAddedPriority={savedAddedPriority}
        sellToken={sellToken}
        buyToken={buyToken}
        ALL_TOKENS={ALL_TOKENS}
        isInFlow={true}
        inSwap={inSwap}
      />
    );
  }, [
    handleConfirm,
    handleClose,
    swapData,
    savedSlippageValue,
    savedAddedPriority,
    sellToken,
    buyToken,
    ALL_TOKENS,
  ]);

  const memoizedPending = useMemo(() => {
    console.log("Memoizing PendingTransaction component");
    return (
      <PendingTransaction
        provider={provider}
        transaction={transaction}
        swapData={swapData}
        chainId={chainId}
        devMode={!transaction}
        devStatus={pendingTxStatus}
        onClose={handleClose}
        isInFlow={true}
      />
    );
  }, [provider, transaction, swapData, chainId, pendingTxStatus, handleClose]);

  return (
    <>
      {activeView === "confirmation" ? memoizedConfirmation : memoizedPending}
    </>
  );
}

export default React.memo(TransactionFlow);
