/* eslint-disable react-hooks/exhaustive-deps */
import React, {
  createContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  useContext,
} from "react";
import { ethers } from "ethers";

import { erc20Abi } from "viem";
import { BlockchainContext } from "../../context/BlockchainContext";
import uniswapRouterABI from "../../constants/abis/UniswapRouter.json";
import wethABI from "../../constants/abis/wethABI.json";
import { CHAINS } from "../../constants/constants";

import getTokenBalance from "../../services/blockchain/getTokenBalance";
import getAmountOutV2 from "../../services/blockchain/getAmountOutV2";
import getUniswapQuoteV3 from "../../services/blockchain/getUniswapQuoteV3";

function DollarValue({ Token, isTokenList, isOutputToken, onValueChange }) {
  const {
    ethDollarPrice,
    savedInputAmount,
    savedOutputAmount,
    chain_id,
    account,
    saverInputAmount,
    authToken,
  } = useContext(BlockchainContext);
  const nativeSymbol = CHAINS[chain_id].nativeSymbol;

  const wethAddress = CHAINS[chain_id].wethAddress;
  const uniswapRouterAddress = CHAINS[chain_id].uniswapRouterAddressV2;

  const [ethPrice, setEthPrice] = useState("");
  const RATE_LIMIT = 500;
  const savedInputAmountRef = useRef(undefined);
  const savedOutputAmountRef = useRef(undefined);
  useEffect(() => {
    const handle = setInterval(() => {
      if (
        String(savedInputAmount.current) !==
          String(savedInputAmountRef.current) ||
        String(savedOutputAmount.current) !==
          String(savedOutputAmountRef.current)
      ) {
        changeTokenPrice();
        savedInputAmountRef.current = savedInputAmount.current;
        savedOutputAmountRef.current = savedOutputAmount.current;
      }
    }, RATE_LIMIT);
    return () => clearInterval(handle);
  }, [ethDollarPrice, account, ethPrice]);

  async function changeTokenPrice() {
    try {
      let oneEthInUSDC = ethDollarPrice.current;
      oneEthInUSDC = Number(oneEthInUSDC);

      if (Token.symbol === nativeSymbol || Token.symbol === "WETH") {
        if (isTokenList) {
          let balance;

          if (Token.symbol === "WETH") {
            try {
              const response = await fetch("/api/rpc-call/get-weth-balance", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${authToken}`,
                },
                body: JSON.stringify({ chain_id, account, wethAddress }),
              });

              const data = await response.json();
              if (response.ok) {
                balance = data.balance;
              } else {
                console.warn("Error:", data.error);
              }
            } catch (error) {
              console.warn("Failed to fetch WETH balance:", error);
            }
            if (balance === 0) {
              setEthPrice("0");
              // Call the callback even for zero values
              if (onValueChange && typeof onValueChange === "function") {
                try {
                  onValueChange("0");
                } catch (error) {
                  console.warn("Error in onValueChange callback:", error);
                }
              }
              return;
            }
          } else if (Token.symbol === nativeSymbol) {
            try {
              const response = await fetch("/api/rpc-call/get-balance", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${authToken}`,
                },
                body: JSON.stringify({ chain_id, account }),
              });

              const data = await response.json();
              if (response.ok) {
                balance = data.balance;
              } else {
                console.warn("Error:", data.error);
              }
            } catch (error) {
              console.warn("Failed to fetch balance:", error);
            }
          }
          balance = ethers.formatEther(balance);
          balance = Number(balance);
          let totalValue = balance * oneEthInUSDC;
          totalValue = totalValue.toFixed(2);

          setEthPrice(totalValue);
          // Call the callback if provided
          if (onValueChange && typeof onValueChange === "function") {
            try {
              onValueChange(totalValue);
            } catch (error) {
              console.warn("Error in onValueChange callback:", error);
            }
          }
        } else {
          let balance;
          if (isOutputToken) {
            balance = savedOutputAmount.current;
          } else {
            balance = savedInputAmount.current;
          }

          balance = Number(balance);
          balance = balance != null ? balance : 0;
          if (balance === 0) {
            setEthPrice("0");
            // Call the callback even for zero values
            if (onValueChange && typeof onValueChange === "function") {
              try {
                onValueChange("0");
              } catch (error) {
                console.warn("Error in onValueChange callback:", error);
              }
            }
            return;
          }
          let totalValue = balance * oneEthInUSDC;
          totalValue = totalValue.toFixed(2);
          if (!isOutputToken) {
            saverInputAmount.current = totalValue;
          }
          setEthPrice(totalValue);
          // Call the callback if provided
          if (onValueChange && typeof onValueChange === "function") {
            try {
              onValueChange(totalValue);
            } catch (error) {
              console.warn("Error in onValueChange callback:", error);
            }
          }
        }
      } else {
        let tokenBalance;

        if (isTokenList) {
          tokenBalance = await getTokenBalance(
            chain_id,
            account,
            Token.address,
            authToken
          );
        } else {
          if (isOutputToken) {
            tokenBalance = savedOutputAmount.current;
          } else {
            tokenBalance = savedInputAmount.current;
          }
          tokenBalance = tokenBalance != null ? tokenBalance : 0;

          if (isNaN(tokenBalance) || tokenBalance === "") {
            tokenBalance = "0";
          } else {
            tokenBalance = String(tokenBalance);
          }

          try {
            tokenBalance = ethers.parseUnits(tokenBalance, Token.decimals);
          } catch (error) {
            tokenBalance = ethers.parseUnits("0", Token.decimals);
          }
        }

        const path = [Token.address, wethAddress];

        let amountOut;
        let amountOutV3;
        try {
          amountOut = await getAmountOutV2(
            chain_id,
            tokenBalance,
            path,
            uniswapRouterAddress,
            authToken
          );
        } catch (error) {}
        try {
          /*           amountOutV3 = await getQuoteV3(
            path[0],
            path[1],
            tokenBalance,
            chain_id
          ); */
          amountOutV3 = await getUniswapQuoteV3(
            path[0],
            path[1],
            tokenBalance,
            chain_id,
            authToken
          );
        } catch (error) {}

        let ethOut;
        if (amountOut && amountOut[1] > amountOutV3) {
          ethOut = amountOut[1];
        } else {
          ethOut = amountOutV3.amountOut;
        }

        /*         if (ETH_TOKENS[toLowerCaseTokenSymbol]?.useV2 === true) {
          ethOut = amountOut[1];
        } */

        ethOut = ethers.formatEther(ethOut);
        ethOut = Number(ethOut);
        let totalDollarValue = ethOut * oneEthInUSDC;

        totalDollarValue = totalDollarValue.toFixed(2);
        if (!isOutputToken) {
          saverInputAmount.current = totalDollarValue;
        }
        setEthPrice(totalDollarValue);
        // Call the callback if provided
        if (onValueChange && typeof onValueChange === "function") {
          try {
            onValueChange(totalDollarValue);
          } catch (error) {
            console.warn("Error in onValueChange callback:", error);
          }
        }
      }
    } catch (error) {
      setEthPrice("0.00");
      // Call the callback even for error cases
      if (onValueChange && typeof onValueChange === "function") {
        try {
          onValueChange("0.00");
        } catch (callbackError) {
          console.warn("Error in onValueChange callback:", callbackError);
        }
      }
      return;
    }
  }

  if (
    ethPrice === "0" ||
    ethPrice === "0.00" ||
    ethPrice === 0 ||
    ethPrice === ""
  ) {
    return <div className="dollar-value">-</div>;
  } else return <div className="dollar-value">≈ ${ethPrice}</div>;
}

export default DollarValue;
