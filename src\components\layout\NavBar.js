"use client";

/* eslint-disable @next/next/no-img-element */
import React, { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import useIsMobile from "../../hooks/useIsMobile";
import CustomConnectButton from "../wallet/CustomConnectButton";
import { HomeIcon, StakeIcon, AboutIcon } from "../common/svgs/NavIcons";
import TabsMode from "../swap/TabsMode";
import TransactionEffect from "../common/TransactionEffect";
import CereDropdown from "../wallet/CereDropdown";
import NetworkDropdown from "../wallet/NetworkDropdown";
import { useTheme } from "../../contexts/ThemeContext";
import ThemeToggle from "../common/ThemeToggle";

function NavBar() {
  const { isDarkMode } = useTheme();
  const [showJuicedTab, setShowJuicedTab] = useState(false);
  const pathname = usePathname();

  const initialActiveLink = () => {
    if (pathname === "/") {
      setShowJuicedTab(true);
      return "home";
    } else if (pathname === "/stake") {
      setShowJuicedTab(false);
      return "stake";
    } else if (pathname === "/about") {
      setShowJuicedTab(false);
      return "about";
    } else {
      return "";
    }
  };
  const [activeLink, setActiveLink] = useState(initialActiveLink);

  const isMobile = useIsMobile();

  useEffect(() => {
    setActiveLink(initialActiveLink());
  }, [pathname]);

  const goToHome = React.useCallback(() => {
    if (activeLink !== "home") {
      window.history.pushState({}, "", "/");
      setActiveLink("home");
      setShowJuicedTab(true);

      window.dispatchEvent(
        new CustomEvent("navchange", { detail: { path: "/" } })
      );
    }
  }, [activeLink]);

  const goToStake = React.useCallback(() => {
    if (activeLink !== "stake") {
      window.history.pushState({}, "", "/stake");
      setActiveLink("stake");
      setShowJuicedTab(false);

      window.dispatchEvent(
        new CustomEvent("navchange", { detail: { path: "/stake" } })
      );
    }
  }, [activeLink]);

  const goToAbout = React.useCallback(() => {
    if (activeLink !== "about") {
      window.history.pushState({}, "", "/about");
      setActiveLink("about");
      setShowJuicedTab(false);

      window.dispatchEvent(
        new CustomEvent("navchange", { detail: { path: "/about" } })
      );
    }
  }, [activeLink]);

  if (isMobile) {
    return (
      <>
        <TransactionEffect />
        <div className="nav-container">
          <div className="nav-left">
            <img
              src={
                isDarkMode
                  ? "/logos/lite-logo-light.svg"
                  : "/logos/lite-logo.svg"
              }
              alt="Pineapple Logo"
              className="logo-image-mobile"
              priority="true"
            />
          </div>
          {showJuicedTab && <TabsMode />}

          <div className="nav-right">
            <ThemeToggle />
            <NetworkDropdown />
            <CereDropdown />
            <CustomConnectButton />
          </div>
        </div>

        <div className="mobile-navbar">
          <div
            className={`mobile-nav-item ${
              activeLink === "home" ? "mobile-nav-active" : ""
            }`}
            onClick={goToHome}
          >
            <HomeIcon active={activeLink === "home"} />
            <span>Swap</span>
          </div>

          <div
            className={`mobile-nav-item ${
              activeLink === "stake" ? "mobile-nav-active" : ""
            }`}
            onClick={goToStake}
          >
            <StakeIcon active={activeLink === "stake"} />
            <span>Stake</span>
          </div>

          <div
            className={`mobile-nav-item ${
              activeLink === "about" ? "mobile-nav-active" : ""
            }`}
            onClick={goToAbout}
          >
            <AboutIcon active={activeLink === "about"} />
            <span>About</span>
          </div>
        </div>
      </>
    );
  } else {
    return (
      <>
        <TransactionEffect />
        <div className="nav-container">
          <div className="nav-left">
            <img
              src={
                isDarkMode
                  ? "/logos/lite-logo-light.svg"
                  : "/logos/lite-logo.svg"
              }
              alt="Pineapple Logo"
              className="logo-image"
              priority="true"
            />
            {/* Desktop-specific navbar content */}
            <div className="nav-buttons">
              <div
                onClick={goToHome}
                className={`nav-button ${
                  activeLink === "home" ? "nav-active" : ""
                }`}
              >
                Trade
              </div>
              <div
                onClick={goToStake}
                className={`nav-button ${
                  activeLink === "stake" ? "nav-active" : ""
                }`}
              >
                Stake
              </div>{" "}
              <div
                onClick={goToAbout}
                className={`nav-button ${
                  activeLink === "about" ? "nav-active" : ""
                }`}
              >
                About
              </div>
            </div>
          </div>
          <div className="nav-right">
            <div className="nav-buttons-group">
              <ThemeToggle />
              <CereDropdown />
              <NetworkDropdown />
              <CustomConnectButton />
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default NavBar;
