"use client";

import React, { useState, useRef, useEffect, useContext } from "react";
import { DownArrow } from "../common/SVGMAIN.js";
import Image from "next/image";
import { useAppKitNetwork } from "@reown/appkit/react";
import { BlockchainContext } from "../../context/BlockchainContext";
import { mainnet, base } from "@reown/appkit/networks";
import useIsMobile from "../../hooks/useIsMobile";

const NetworkDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const { chainId, switchNetwork } = useAppKitNetwork();
  const { chain_id } = useContext(BlockchainContext);
  const isMobile = useIsMobile();

  const availableNetworks = [
    {
      id: 1,
      name: "Ethereum",
      icon: "/logos/eth_.png",
      chainId: 1,
      key: "eip155:1",
      networkObject: mainnet,
    },
    {
      id: 8453,
      name: "Base",
      icon: "/logos/base.png",
      chainId: 8453,
      key: "eip155:8453",
      networkObject: base,
    },
  ];

  const getCurrentNetwork = () => {
    if (chainId) {
      const chainIdNum =
        typeof chainId === "string" && chainId.includes(":")
          ? parseInt(chainId.split(":")[1])
          : parseInt(chainId);

      const networkFromChainId = availableNetworks.find(
        (network) => network.chainId === chainIdNum || network.id === chainIdNum
      );

      if (networkFromChainId) {
        console.log(
          `Found network from chainId: ${chainIdNum}`,
          networkFromChainId.name
        );
        return networkFromChainId;
      }
    }

    if (chain_id) {
      const chainIdNum = parseInt(chain_id);
      const networkFromContextId = availableNetworks.find(
        (network) => network.id === chainIdNum || network.chainId === chainIdNum
      );

      if (networkFromContextId) {
        console.log(
          `Found network from context chain_id: ${chainIdNum}`,
          networkFromContextId.name
        );
        return networkFromContextId;
      }
    }

    console.log("No matching network found, defaulting to Ethereum");
    return availableNetworks[0];
  };

  const currentNetwork = getCurrentNetwork();

  const handleNetworkSelect = (networkObject) => {
    try {
      const networkChainId = networkObject.chainId;
      if (
        chainId === networkChainId ||
        (chainId === 1 && networkChainId === 1) ||
        (chainId === 8453 && networkChainId === 8453)
      ) {
        setIsOpen(false);
        return;
      }

      setIsOpen(false);

      if (typeof window === "undefined") {
        console.warn("Cannot switch network on server side");
        return;
      }

      console.log(`Switching to network: ${networkObject.name}`);

      try {
        switchNetwork(networkObject);
        console.log(`Network switch requested to ${networkObject.name}`);
      } catch (error) {
        console.warn("Error switching network:", error);
        openNetworkModal();
      }
    } catch (error) {
      console.warn("Error in network selection:", error);
      openNetworkModal();
    }
  };

  const openNetworkModal = () => {
    console.warn(
      "Direct network switching not available, opening modal instead"
    );

    if (typeof window !== "undefined" && window.reown && window.reown.appkit) {
      const appKit = window.reown.appkit;

      if (appKit.modal && typeof appKit.modal.open === "function") {
        console.log("Using appKit.modal.open() for network switching");
        appKit.modal.open();
      } else if (typeof appKit.openModal === "function") {
        console.log("Using appKit.openModal() for network switching");
        appKit.openModal();
      } else {
        console.log(
          "No direct method found, dispatching event for network switching"
        );
        const event = new CustomEvent("open-wallet-modal");
        window.dispatchEvent(event);
      }
    } else {
      console.log("AppKit not found, dispatching event for network switching");
      const event = new CustomEvent("open-wallet-modal");
      window.dispatchEvent(event);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div
      className="cere-dropdown-container network-dropdown-container"
      ref={dropdownRef}
    >
      <div
        className={`wingman-container ${isMobile ? "wingman-mobile" : ""}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <Image
          src={currentNetwork.icon}
          alt={`${currentNetwork.name} Logo`}
          width={24}
          height={24}
          className="cere-logo-image"
          style={{ marginRight: "8px", objectFit: "contain" }}
        />
        {!isMobile && <span>{currentNetwork.name}</span>}
        {!isMobile && (
          <div className="arrow-container">
            <DownArrow />
          </div>
        )}
      </div>

      {isOpen && (
        <>
          {isMobile && (
            <div
              className="mobile-dropdown-backdrop"
              onClick={() => setIsOpen(false)}
            />
          )}
          <div className="cere-dropdown-content network-dropdown-content">
            <div className="cere-info">
              <h4>Select Network</h4>
              <p>Choose which blockchain network you want to use.</p>
            </div>
            <div className="network-list">
              {availableNetworks.map((network) => (
                <div
                  key={network.id}
                  className={`network-item ${
                    currentNetwork.id === network.id
                      ? "network-item-active"
                      : ""
                  }`}
                  onClick={() => handleNetworkSelect(network.networkObject)}
                >
                  <div className="network-icon">
                    <Image
                      src={network.icon}
                      alt={`${network.name} Logo`}
                      width={20}
                      height={20}
                      style={{ objectFit: "contain" }}
                    />
                  </div>
                  <span>{network.name}</span>
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default NetworkDropdown;
