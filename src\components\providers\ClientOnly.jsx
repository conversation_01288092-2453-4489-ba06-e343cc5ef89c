"use client";

import { useEffect, useState } from "react";

/**
 * ClientOnly is a wrapper component that ensures its children are only rendered on the client side.
 * This is useful for components that use browser APIs or need to be hydrated on the client.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The components to render on the client side
 * @param {React.ReactNode} props.fallback - Optional fallback component to show during SSR (defaults to null)
 * @returns {React.ReactNode} The children components or fallback
 */
export default function ClientOnly({ children, fallback = null }) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient ? children : fallback;
}
