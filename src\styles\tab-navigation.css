/* Tab Navigation Styles */
.tab-navigation {
  display: flex;
  justify-content: center;
  margin: 20px 0;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  padding: 5px;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
  z-index: 10;
  position: relative;
}

.tab {
  padding: 10px 20px;
  margin: 0 5px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
  color: #fff;
  position: relative;
  overflow: hidden;
}

.tab:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.tab.active {
  background-color: var(--primary-color);
  color: #fff;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

.content-container {
  transition: opacity 0.3s ease;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .tab {
    padding: 8px 15px;
    font-size: 0.9rem;
  }
}
