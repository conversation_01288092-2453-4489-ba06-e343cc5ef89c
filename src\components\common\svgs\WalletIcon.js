"use client";

// Import React, needed for defining the component
import React from "react";
import { useTheme } from "../../../contexts/ThemeContext";

// Define a functional component that returns your SVG
const WalletIcon = () => {
  const { isDarkMode } = useTheme();

  // Return different SVG based on theme
  if (!isDarkMode) {
    // Light mode wallet icon (filled black version)
    return (
      <svg
        width="18"
        height="16"
        viewBox="0 0 18 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.75 3.5H3.75C3.10575 3.5 2.496 3.221 2.07375 2.75075C2.48625 2.29025 3.0855 2 3.75 2H17.25C18.231 1.9955 18.2302 0.50375 17.25 0.5H3.75C1.67925 0.5 0 2.17925 0 4.25V11.75C0 13.8208 1.67925 15.5 3.75 15.5H15.75C16.9928 15.5 18 14.4928 18 13.25V5.75C18 4.50725 16.9928 3.5 15.75 3.5Z"
          fill="black"
        />
      </svg>
    );
  }

  // Dark mode wallet icon (outlined version)
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14 4H2C1.44772 4 1 4.44772 1 5V13C1 13.5523 1.44772 14 2 14H14C14.5523 14 15 13.5523 15 13V5C15 4.44772 14.5523 4 14 4Z"
        stroke="#A9A9A9"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 2L8 4L5 2"
        stroke="#A9A9A9"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 8H12.01"
        stroke="#A9A9A9"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

// Export the component for use in other parts of your application
export default WalletIcon;
