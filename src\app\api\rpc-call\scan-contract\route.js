import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";

// RPC URLs for different chains
const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

async function initializeProvider(chain_id, retries = 3, delayMs = 3000) {
  try {
    const provider = new ethers.JsonRpcProvider(RPC_URLS[chain_id]);

    await provider.getNetwork();
    return provider;
  } catch (error) {
    if (retries > 0) {
      console.warn(`Network detection failed, retrying in ${delayMs}ms...`);
      await new Promise((resolve) => setTimeout(resolve, delayMs));
      return initializeProvider(chain_id, retries - 1, delayMs);
    } else {
      throw new Error(`Failed to detect network after retries: ${error}`);
    }
  }
}

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }

    const authenticatedAddress = authResult.address;

    const body = await request.json();
    const { contractAddress, chain_id } = body;

    console.log("Contract Address:", contractAddress);
    console.log("Chain ID:", chain_id);

    if (!contractAddress || !chain_id) {
      return NextResponse.json(
        { error: "Missing contract address or chain_id" },
        { status: 400 }
      );
    }

    const provider = await initializeProvider(chain_id);

    const response = await provider.send("gp_tokenSecurity", [contractAddress]);
    const result = response[contractAddress.toLowerCase()];

    if (!result) {
      return NextResponse.json(
        { error: "No data found for this contract" },
        { status: 404 }
      );
    }

    return NextResponse.json({ result });
  } catch (error) {
    console.warn("Error scanning contract:", error);
    return NextResponse.json(
      { error: "Failed to scan contract" },
      { status: 500 }
    );
  }
}
