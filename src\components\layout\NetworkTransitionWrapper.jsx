import React, { useContext } from "react";
import { BlockchainContext } from "../../context/BlockchainContext";
import NetworkTransitionOverlay from "./NetworkTransitionOverlay";

const NetworkTransitionWrapper = ({ children }) => {
  const { isNetworkChanging } = useContext(BlockchainContext);

  return (
    <>
      {children}
      <NetworkTransitionOverlay isVisible={isNetworkChanging} />
    </>
  );
};

export default NetworkTransitionWrapper;
