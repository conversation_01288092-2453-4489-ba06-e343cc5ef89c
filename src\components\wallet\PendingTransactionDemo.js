'use client';

import React, { useState } from "react";
import PendingTransaction from "./PendingTransaction";

function PendingTransactionDemo() {
  const [showDemo, setShowDemo] = useState(false);
  const [demoStatus, setDemoStatus] = useState("pending");

  const toggleDemo = () => {
    setShowDemo(!showDemo);
  };

  const changeStatus = (status) => {
    setDemoStatus(status);
  };

  return (
    <div className="demo-container">
      <button
        className="demo-button"
        onClick={toggleDemo}
        style={{
          padding: "10px 15px",
          background: "var(--box-inner)",
          border: "1px solid var(--border)",
          borderRadius: "20px",
          color: "var(--text)",
          cursor: "pointer",
          margin: "10px",
          fontSize: "0.9rem",
          zIndex: 1000,
        }}
      >
        {showDemo ? "Hide" : "Show"} Transaction Demo
      </button>

      {/*    {showDemo && (
        <div
          className="demo-controls"
          style={{ marginBottom: "10px", zIndex: 1000 }}
        >
          <button
            onClick={() => changeStatus("pending")}
            style={{
              padding: "8px 12px",
              background:
                demoStatus === "pending"
                  ? "var(--primary-color)"
                  : "var(--box-inner)",
              border: "1px solid var(--border)",
              borderRadius: "15px",
              color: demoStatus === "pending" ? "#000" : "var(--text)",
              cursor: "pointer",
              margin: "0 5px",
              fontSize: "0.8rem",
            }}
          >
            Pending
          </button>
          <button
            onClick={() => changeStatus("confirmed")}
            style={{
              padding: "8px 12px",
              background:
                demoStatus === "confirmed"
                  ? "var(--primary-color)"
                  : "var(--box-inner)",
              border: "1px solid var(--border)",
              borderRadius: "15px",
              color: demoStatus === "confirmed" ? "#000" : "var(--text)",
              cursor: "pointer",
              margin: "0 5px",
              fontSize: "0.8rem",
            }}
          >
            Confirmed
          </button>
          <button
            onClick={() => changeStatus("failed")}
            style={{
              padding: "8px 12px",
              background:
                demoStatus === "failed"
                  ? "var(--primary-color)"
                  : "var(--box-inner)",
              border: "1px solid var(--border)",
              borderRadius: "15px",
              color: demoStatus === "failed" ? "#000" : "var(--text)",
              cursor: "pointer",
              margin: "0 5px",
              fontSize: "0.8rem",
            }}
          >
            Failed
          </button>
        </div>
      )} */}

      {showDemo && (
        <PendingTransaction
          transaction={null}
          chainId={1}
          devMode={true}
          devStatus={demoStatus}
        />
      )}
    </div>
  );
}

export default PendingTransactionDemo;
