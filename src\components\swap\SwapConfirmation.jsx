import React, { useState, useEffect, memo } from "react";
import Loader from "../common/Loader";
import { ethers } from "ethers";
import DollarValue from "../common/DollarValue";

const styles = {
  tokenContainer: {
    padding: "5px 0",
  },
  tokenRow: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  tokenAmountContainer: {
    display: "flex",
    alignItems: "center",
    gap: "10px",
  },
  tokenAmount: {
    fontSize: "1.5rem",
    fontWeight: "500",
  },
  tokenImage: {
    borderRadius: "50%",
    width: "40px",
    height: "40px",
  },
  dollarValue: {
    color: "#999",
    marginTop: "2px",
  },

  arrowContainer: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    height: "30px",
    margin: "5px 0",
    width: "100%",
  },
  arrowIcon: {
    color: "var(--primary-color)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },

  headerContainer: {
    borderBottom: "none",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    paddingInline: "10px",
  },
  headerTitle: {
    fontSize: "1.1rem",
    color: "#ccc",
  },
  closeButton: {
    fontSize: "1.5rem",
    cursor: "pointer",
  },
};

function SwapConfirmation({
  onConfirm,
  onCancel,
  swapData,
  savedSlippageValue,
  savedAddedPriority,
  sellToken,
  buyToken,
  ALL_TOKENS,
  isInFlow = false,
  inSwap,
}) {
  const [isClosing, setIsClosing] = useState(false);

  const handleClose = () => {
    inSwap.current = false;
    setIsClosing(true);
    onCancel();
  };

  const handleConfirm = () => {
    if (!isInFlow) {
      setIsClosing(true);
    }
    onConfirm();
  };

  if (isClosing && !isInFlow) {
    return null;
  }

  const formatTokenAmount = (amount, isReceiveAmount = false) => {
    if (!amount) return "0";

    try {
      const tokenDecimals = isReceiveAmount
        ? ALL_TOKENS[buyToken]?.decimals || 18
        : ALL_TOKENS[sellToken]?.decimals || 18;

      const formattedAmount = ethers.formatUnits(amount, tokenDecimals);

      const num = parseFloat(formattedAmount);
      if (isNaN(num)) return "0";

      if (num > 1000000) {
        return num.toLocaleString(undefined, {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2,
        });
      }

      if (num < 0.000001 && num > 0) {
        return num.toExponential(2);
      }

      if (num > 1) {
        return num.toLocaleString(undefined, {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2,
        });
      }

      return num.toLocaleString(undefined, {
        maximumFractionDigits: 8,
        minimumFractionDigits: 2,
      });
    } catch (e) {
      console.warn("Error formatting amount:", e);

      try {
        const num = parseFloat(amount);
        if (isNaN(num)) return "0";

        if (num > 1000000) {
          return num.toLocaleString(undefined, {
            maximumFractionDigits: 2,
            minimumFractionDigits: 2,
          });
        }

        return num.toLocaleString();
      } catch (err) {
        return "0";
      }
    }
  };

  const sellTokenSymbol = ALL_TOKENS[sellToken]?.symbol || "Token";
  const buyTokenSymbol = ALL_TOKENS[buyToken]?.symbol || "Token";

  console.log("SwapConfirmation rendering with:", {
    swapData: swapData ? "present" : "missing",
    sellToken,
    buyToken,
    sellTokenSymbol,
    buyTokenSymbol,
    amountIn: swapData?.amountIn || swapData?.amount,
    amountOut: swapData?.buyAmount || swapData?.amountOut,
  });

  if (!swapData) {
    console.warn("SwapConfirmation: Missing swapData!");
    return (
      <div className="pending-container">
        <div className="pending-box">
          <div className="pending-header" style={styles.headerContainer}>
            <div className="pending-title" style={styles.headerTitle}>
              Error: Missing Swap Data
            </div>
            <div
              className="close-button"
              onClick={handleClose}
              style={styles.closeButton}
            >
              ×
            </div>
          </div>
          <div className="confirmation-content">
            <div className="general-box">
              <p>
                There was an error loading the swap details. Please try again.
              </p>
            </div>
          </div>
          <div className="confirmation-actions">
            <button className="cancel-button" onClick={handleClose}>
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  inSwap.current = true;
  return (
    <div className="pending-container">
      <div className="pending-box">
        <div className="confirmation-content">
          <div className="general-box">
            {/* From Token */}
            <div style={styles.tokenContainer}>
              <div style={styles.tokenRow}>
                <div style={styles.tokenAmountContainer}>
                  <div style={styles.tokenAmount}>
                    {formatTokenAmount(swapData?.amountIn || swapData?.amount)}{" "}
                    {sellTokenSymbol}
                  </div>
                </div>
                <img
                  src={ALL_TOKENS[sellToken]?.logo_uri}
                  alt={sellTokenSymbol}
                  style={styles.tokenImage}
                />
              </div>
              <div style={styles.dollarValue}>
                <DollarValue
                  Token={ALL_TOKENS[sellToken]}
                  isOutputToken={false}
                />
              </div>
            </div>

            {/* Down Arrow */}
            <div style={styles.arrowContainer}>
              <div style={styles.arrowIcon}>
                <svg
                  width="28"
                  height="28"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 4L12 20"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M18 14L12 20L6 14"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>

            {/* To Token */}
            <div style={styles.tokenContainer}>
              <div style={styles.tokenRow}>
                <div style={styles.tokenAmountContainer}>
                  <div style={styles.tokenAmount}>
                    {formatTokenAmount(
                      swapData?.buyAmount || swapData?.amountOut,
                      true
                    )}{" "}
                    {buyTokenSymbol}
                  </div>
                </div>
                <img
                  src={ALL_TOKENS[buyToken]?.logo_uri}
                  alt={buyTokenSymbol}
                  style={styles.tokenImage}
                />
              </div>
              <div style={styles.dollarValue}>
                <DollarValue
                  Token={ALL_TOKENS[buyToken]}
                  isOutputToken={true}
                />
              </div>
            </div>
          </div>

          <SaverInfo
            swapData={swapData}
            savedSlippageValue={savedSlippageValue}
            savedAddedPriority={savedAddedPriority}
            buyToken={buyToken}
            ALL_TOKENS={ALL_TOKENS}
          />
        </div>

        <div className="confirmation-actions">
          <button className="cancel-button" onClick={handleClose}>
            Cancel
          </button>
          <button className="confirm-button" onClick={handleConfirm}>
            Confirm Swap
          </button>
        </div>
      </div>
    </div>
  );
}

function SaverInfo({
  swapData,
  savedSlippageValue,
  savedAddedPriority,
  buyToken,
  ALL_TOKENS,
}) {
  const [slippagePercentage] = useState(savedSlippageValue.current || 0.5);
  const [minTokensOut, setMinTokensOut] = useState("0");
  const [pairRoute, setPairRoute] = useState("");
  const [gasType, setGasType] = useState("Auto");
  const [gasGwei, setGasGwei] = useState("1");
  const [loading, setLoading] = useState(true);

  const formatTokenAmount = (amount) => {
    if (!amount) return "0";

    try {
      const tokenDecimals =
        ALL_TOKENS[buyToken]?.decimals || swapData?.decimals || 18;

      const formattedAmount = ethers.formatUnits(amount, tokenDecimals);

      const num = parseFloat(formattedAmount);
      if (isNaN(num)) return "0";

      if (num > 1000000) {
        return num.toLocaleString(undefined, {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2,
        });
      }

      if (num < 0.000001 && num > 0) {
        return num.toExponential(2);
      }

      if (num > 1) {
        return num.toLocaleString(undefined, {
          maximumFractionDigits: 2,
          minimumFractionDigits: 2,
        });
      }

      return num.toLocaleString(undefined, {
        maximumFractionDigits: 8,
        minimumFractionDigits: 2,
      });
    } catch (e) {
      console.warn("Error formatting amount:", e);

      try {
        const num = parseFloat(amount);
        if (isNaN(num)) return "0";

        if (num > 1000000) {
          return num.toLocaleString(undefined, {
            maximumFractionDigits: 2,
            minimumFractionDigits: 2,
          });
        }

        return num.toLocaleString();
      } catch (err) {
        return "0";
      }
    }
  };

  useEffect(() => {
    if (swapData) {
      console.log("swapData", swapData);
      console.log("savedAddedPriority in SaverInfo:", savedAddedPriority);

      try {
        const buyAmount = swapData.buyAmount || swapData.amountOut;
        if (buyAmount) {
          const slippageMultiplier = (100 - slippagePercentage) / 100;
          const minOut =
            (BigInt(buyAmount) *
              BigInt(Math.floor(slippageMultiplier * 1000))) /
            BigInt(1000);
          setMinTokensOut(minOut.toString());
        } else {
          setMinTokensOut("0");
        }
      } catch (error) {
        console.warn("Error calculating min tokens out:", error);
        setMinTokensOut("0");
      }

      setPairRoute(swapData.pairRoute || "V2");

      let gasValue = "1";
      let gasTypeValue = "Auto";

      if (savedAddedPriority && savedAddedPriority.current) {
        const priorityType = savedAddedPriority.current;

        if (priorityType === "custom") {
          const customGasValue = swapData.priorityGas || "5";
          gasValue = customGasValue;
          gasTypeValue = "Custom";
        } else if (priorityType === "auto") {
          gasValue = "2";
          gasTypeValue = "Auto";
        } else if (priorityType === 2) {
          gasValue = "2";
          gasTypeValue = "Average";
        } else if (priorityType === 3) {
          gasValue = "3";
          gasTypeValue = "Quick";
        } else if (priorityType === 5) {
          gasValue = "5";
          gasTypeValue = "Instant";
        } else {
          gasValue = priorityType.toString();
          gasTypeValue = "Custom";
        }
      }

      console.log(`Setting gas values: ${gasValue} Gwei (${gasTypeValue})`);
      setGasGwei(gasValue);
      setGasType(gasTypeValue);
      setLoading(false);
    }
  }, [swapData, slippagePercentage, savedAddedPriority]);

  if (loading) {
    return (
      <div className="general-box">
        <div className="saver-text-container">
          <div className="saver-text-left">Route</div>
          <div className="saver-text-right"></div>
        </div>{" "}
        <div className="saver-text-container">
          <div className="saver-text-left">Min Tokens Out</div>
          <div className="saver-text-right"></div>
        </div>{" "}
        <div className="saver-text-container">
          <div className="saver-text-left">Slippage</div>
          <div className="saver-text-right"></div>
        </div>
        <div className="saver-text-container">
          <div className="saver-text-left">Gas</div>
          <div className="saver-text-right"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="general-box">
      <div className="saver-text-container">
        <div className="saver-text-left">Route</div>
        <div
          className={`saver-text-right ${
            pairRoute === "V3"
              ? "route-v3"
              : pairRoute === "V2"
              ? "route-v2"
              : "route-v2-to-v3"
          }`}
        >
          {pairRoute}
        </div>
      </div>
      <div className="saver-text-container">
        <div className="saver-text-left">Min Tokens Out</div>
        <div className="saver-text-right">
          {formatTokenAmount(minTokensOut)}
        </div>
      </div>
      <div className="saver-text-container">
        <div className="saver-text-left">Slippage</div>
        <div className="saver-text-right">{`${Number(
          slippagePercentage
        ).toFixed(2)}%`}</div>
      </div>
      <div className="saver-text-container">
        <div className="saver-text-left">Gas</div>
        <div className="saver-text-right">
          <span>{`${gasGwei} Gwei`}</span>
          <span className="gas-indicator">
            <span
              className={`gas-indicator-dot gas-${gasType.toLowerCase()}`}
            ></span>
            <span>{` (${gasType})`}</span>
          </span>
        </div>
      </div>
    </div>
  );
}

export default memo(SwapConfirmation);
