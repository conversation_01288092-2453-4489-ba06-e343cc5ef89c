/** @type {import('next').NextConfig} */

require("dotenv").config({ path: ".env.local" });

const baseConfig = require("./next.config.js");

const devConfig = {
  ...baseConfig,

  experimental: {
    ...baseConfig.experimental,
  },

  allowedDevOrigins: true,

  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS",
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "X-Requested-With, Content-Type, Authorization",
          },
          {
            key: "Access-Control-Allow-Credentials",
            value: "true",
          },
        ],
      },
    ];
  },
};

module.exports = devConfig;
