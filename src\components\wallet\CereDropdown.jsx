"use client";

import React, { useState, useRef, useEffect } from "react";
import { DownArrow } from "../common/SVGMAIN.js";
import Image from "next/image";
import useIsMobile from "../../hooks/useIsMobile";

const CereDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isCereEnabled, setIsCereEnabled] = useState(false);
  const dropdownRef = useRef(null);
  const isMobile = useIsMobile();

  useEffect(() => {
    const cereEnabled =
      localStorage.getItem("cere_tracking_enabled") === "true";
    setIsCereEnabled(cereEnabled);
  }, []);

  const handleToggle = () => {
    const newState = !isCereEnabled;
    setIsCereEnabled(newState);
    localStorage.setItem("cere_tracking_enabled", newState ? "true" : "false");

    console.log(
      `Setting cere_tracking_enabled to ${newState ? "true" : "false"}`
    );
    setTimeout(() => {
      console.log(
        `Reloading page with cere_tracking_enabled = ${localStorage.getItem(
          "cere_tracking_enabled"
        )}`
      );
      window.location.reload();
    }, 100);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="cere-dropdown-container" ref={dropdownRef}>
      <div className="wingman-container" onClick={() => setIsOpen(!isOpen)}>
        <Image
          src="/logos/cere.png"
          alt="Cere Logo"
          width={24}
          height={24}
          className="cere-logo-image"
          style={{ marginRight: "8px", objectFit: "contain" }}
        />
        {!isMobile && <span>Wingman</span>}
        <div className="status-dot-wrapper">
          <div className={`status-dot ${isCereEnabled ? "live" : ""}`} />
        </div>
      </div>

      {isOpen && (
        <>
          {isMobile && (
            <div
              className="mobile-dropdown-backdrop"
              onClick={() => setIsOpen(false)}
            />
          )}
          <div className="cere-dropdown-content">
            <div className="cere-info">
              <h4>What is Wingman?</h4>
              <p>
                Wingman enables Cere to track your DEX actions & store data on a
                decentralized network, rewarding you with potential airdrops and
                other benefits.
              </p>
            </div>
            <div
              className={`action-button ${
                isCereEnabled ? "action-button-active" : ""
              }`}
              onClick={handleToggle}
            >
              {isCereEnabled ? "Disable" : "Enable"}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default CereDropdown;
