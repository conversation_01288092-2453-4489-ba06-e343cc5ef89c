import { BASE_TOKENS } from "../constants";

function normalizeTokenProperties(token) {
  return {
    id: token.id || null,
    is_partner: token.isPartner || token.is_partner || false,
    is_v2: token.isV2 || token.is_v2 || false,
    chain_id: token.chainId || token.chain_id || null,
    name: token.name || "",
    symbol: token.symbol || "",
    decimals: token.decimals || null,
    address: token.address || "",
    logo_uri: token.logoURI || token.logo_uri || "",
  };
}

function mergeTokens(chain_id, ETH_TOKENS) {
  let customTokens = {};

  if (typeof window !== "undefined" && window.localStorage) {
    try {
      customTokens = JSON.parse(localStorage.getItem("customTokens")) || {};

      Object.keys(customTokens).forEach((key) => {
        customTokens[key] = normalizeTokenProperties(customTokens[key]);
      });
    } catch (error) {
      console.warn("Error parsing custom tokens from localStorage", error);
    }
  }

  const mergedTokens = {};
  let maxId = 0;

  Object.keys(ETH_TOKENS).forEach((key) => {
    const token = ETH_TOKENS[key];
    if (token.chain_id === chain_id) {
      mergedTokens[token.id || key] = token;

      maxId = Math.max(maxId, token.id || 0);
    }
  });

  if (chain_id === 8453) {
    Object.keys(BASE_TOKENS).forEach((key) => {
      const token = BASE_TOKENS[key];
      if (token.chain_id === chain_id) {
        mergedTokens[token.id] = token;
        maxId = Math.max(maxId, token.id || 0);
      }
    });
  }

  /*   Object.keys(customTokens).forEach((key) => {
    const token = customTokens[key];
    

    if (token.chain_id === chain_id) {
      
      if (!token.id) {
        maxId += 1;
        token.id = maxId;
      }
      mergedTokens[token.id] = token;
    }
  }); */
  Object.keys(customTokens).forEach((key) => {
    const token = customTokens[key];

    const isDuplicate = Object.values(mergedTokens).some(
      (mergedToken) =>
        mergedToken.symbol === token.symbol ||
        mergedToken.contractAddress === token.contractAddress
    );

    if (token.chain_id === chain_id && !isDuplicate) {
      if (!token.id) {
        maxId += 1;
        token.id = maxId;
      }
      mergedTokens[token.id] = token;
    }
  });

  return mergedTokens;
}

export default mergeTokens;
