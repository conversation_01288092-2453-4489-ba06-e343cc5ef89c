const fs = require("fs");
const path = require("path");
const { promisify } = require("util");
const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const stat = promisify(fs.stat);

const rootDir = path.resolve(__dirname);
const srcDir = path.join(rootDir, "src");

const className = process.argv[2];

if (!className) {
  console.error("Please provide a CSS class name to search for.");
  console.error("Usage: node find-css-usage.js <className>");
  process.exit(1);
}

async function getFiles(dir, extensions) {
  const subdirs = await readdir(dir);
  const files = await Promise.all(
    subdirs.map(async (subdir) => {
      const res = path.resolve(dir, subdir);
      return (await stat(res)).isDirectory() ? getFiles(res, extensions) : res;
    })
  );
  return files
    .flat()
    .filter((file) => extensions.includes(path.extname(file).toLowerCase()));
}

async function findClassUsage(file, className) {
  try {
    const content = await readFile(file, "utf8");
    const lines = content.split("\n");
    const occurrences = [];

    const regex = new RegExp(className, "g");

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (regex.test(line)) {
        const startLine = Math.max(0, i - 2);
        const endLine = Math.min(lines.length - 1, i + 2);
        const context = lines
          .slice(startLine, endLine + 1)
          .map((l, idx) => {
            const lineNumber = startLine + idx + 1;
            const prefix = lineNumber === i + 1 ? "> " : "  ";
            return `${prefix}${lineNumber}: ${l}`;
          })
          .join("\n");

        occurrences.push({
          line: i + 1,
          context,
        });
      }
    }

    return occurrences;
  } catch (error) {
    console.error(`Error reading file ${file}:`, error);
    return [];
  }
}

async function findAllClassUsages(className) {
  try {
    console.log(`Searching for usages of CSS class: "${className}"\n`);

    const allFiles = await getFiles(srcDir, [
      ".js",
      ".jsx",
      ".ts",
      ".tsx",
      ".html",
      ".css",
    ]);
    console.log(`Searching in ${allFiles.length} files...\n`);

    const usages = [];

    for (const file of allFiles) {
      const occurrences = await findClassUsage(file, className);

      if (occurrences.length > 0) {
        usages.push({
          file: path.relative(rootDir, file),
          occurrences,
        });
      }
    }

    if (usages.length === 0) {
      console.log(`No usages found for class "${className}".`);
    } else {
      console.log(`Found ${usages.length} files using class "${className}":\n`);

      for (const usage of usages) {
        console.log(`File: ${usage.file}`);
        console.log(`Occurrences: ${usage.occurrences.length}`);

        for (const occurrence of usage.occurrences) {
          console.log(`\nLine ${occurrence.line}:`);
          console.log(occurrence.context);
          console.log("-".repeat(80));
        }

        console.log("\n");
      }
    }
  } catch (error) {
    console.error("Error:", error);
  }
}

findAllClassUsages(className);
