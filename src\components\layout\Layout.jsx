"use client";

import { useContext, useMemo, useState, useEffect } from "react";
import { BlockchainContext } from "../../context/BlockchainContext";
import { usePathname } from "next/navigation";
import NavBar from "./NavBar";
import FooterBar from "./Footer";
import { BASE_TOKENS, ETH_TOKENS_DISPLAY } from "../../constants/constants";
import Swap from "../swap/Swap";
import AboutContent from "../pages/AboutContent";
import StakeContent from "../pages/StakeContent";

const Layout = ({ buyLink, buyLinkKey }) => {
  const { chain_id, account } = useContext(BlockchainContext);
  const pathname = usePathname();
  const [activeContent, setActiveContent] = useState("swap");

  useEffect(() => {
    if (pathname === "/") {
      setActiveContent("swap");
    } else if (pathname === "/about") {
      setActiveContent("about");
    } else if (pathname === "/stake") {
      setActiveContent("stake");
    }
  }, [pathname]);

  useEffect(() => {
    const handleNavChange = (event) => {
      const { path } = event.detail;
      if (path === "/") {
        setActiveContent("swap");
      } else if (path === "/about") {
        setActiveContent("about");
      } else if (path === "/stake") {
        setActiveContent("stake");
      }
    };

    window.addEventListener("navchange", handleNavChange);
    return () => {
      window.removeEventListener("navchange", handleNavChange);
    };
  }, []);

  const memoNavBar = useMemo(() => <NavBar />, [account]);
  const memoFooterBar = useMemo(() => <FooterBar />, [account]);

  const swapContent = useMemo(
    () => (
      <Swap
        buyLink={buyLink}
        buyLinkKey={buyLinkKey}
        chain_id={chain_id}
        key={chain_id}
        ALL_TOKENS={chain_id === 8453 ? BASE_TOKENS : ETH_TOKENS_DISPLAY}
      />
    ),
    [buyLink, buyLinkKey, chain_id]
  );

  const aboutContent = useMemo(() => <AboutContent />, []);
  const stakeContent = useMemo(() => <StakeContent />, []);

  const renderContent = () => {
    switch (activeContent) {
      case "swap":
        return swapContent;
      case "about":
        return aboutContent;
      case "stake":
        return stakeContent;
      default:
        return null;
    }
  };

  return (
    <div className="whole-container">
      {memoNavBar}
      <div className="bg" />

      {/* Main Content */}
      {renderContent()}

      {memoFooterBar}
    </div>
  );
};

export default Layout;
