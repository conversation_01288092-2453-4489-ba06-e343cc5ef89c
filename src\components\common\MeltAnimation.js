import React, { useContext, useEffect, useState } from "react";
import { BlockchainContext } from "../../context/BlockchainContext";
import useIsMobile from "../../hooks/useIsMobile";

const MeltAnimation = () => {
  const { isJuicedMode } = useContext(BlockchainContext);
  const [showAnimation, setShowAnimation] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (isJuicedMode) {
      setShowAnimation(true);

      const timer = setTimeout(() => {
        setShowAnimation(false);
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, [isJuicedMode]);

  if (!showAnimation || !isJuicedMode || isMobile) return null;

  return (
    <section className="melt-animation-area">
      <img
        className="melt-desktop"
        src={`/images/pnga-melt.png?t=${Date.now()}`}
        alt="Melt Animation"
      />
    </section>
  );
};

export default MeltAnimation;
