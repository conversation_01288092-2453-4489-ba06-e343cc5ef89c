"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { useTheme } from "../contexts/ThemeContext";

export default function NotFound() {
  const { isDarkMode } = useTheme();
  return (
    <div className="not-found-container">
      <div className="nav-container">
        <div className="nav-left">
          <img
            src={
              isDarkMode ? "/logos/lite-logo-light.svg" : "/logos/lite-logo.svg"
            }
            alt="Pineapple Logo"
            className="logo-image"
            priority="true"
          />
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="not-found-content"
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "60vh",
          padding: "2rem",
          textAlign: "center",
        }}
      >
        <h1>404 - Page Not Found</h1>
        <p>The page you are looking for does not exist.</p>
        <Link href="/">
          <button
            className="return-home-button"
            style={{
              background: "var(--primary-color)",
              color: "white",
              border: "none",
              borderRadius: "20px",
              padding: "10px 20px",
              fontSize: "16px",
              cursor: "pointer",
              marginTop: "20px",
              transition: "all 0.3s ease",
            }}
          >
            Return Home
          </button>
        </Link>
      </motion.div>

      <div className="footer-container" style={{ marginTop: "auto" }}>
        <div className="footer-content">
          <p>© 2024 Pineapple DEX. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}
