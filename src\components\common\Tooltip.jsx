"use client";

import React, { useState, useRef, useEffect } from "react";

const Tooltip = ({ info }) => {
  const [visible, setVisible] = useState(false);
  const tooltipRef = useRef(null); // Reference to the tooltip container

  // Function to handle outside clicks
  const handleClickOutside = (event) => {
    if (tooltipRef.current && !tooltipRef.current.contains(event.target)) {
      setVisible(false);
    }
  };

  useEffect(() => {
    // Add when mounted
    document.addEventListener("mousedown", handleClickOutside);
    // Return function to be called when unmounted
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div
      ref={tooltipRef} // Attach the ref
      className="tooltip-container"
      onClick={() => setVisible((prev) => !prev)}
    >
      {!visible && (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="16" x2="12" y2="12"></line>
          <line x1="12" y1="8" x2="12.01" y2="8"></line>
        </svg>
      )}{" "}
      {visible && <div className="tooltip-box">{info}</div>}
    </div>
  );
};

export default Tooltip;
