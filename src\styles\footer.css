/* Footer Styles */
.footer-container {
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  padding-bottom: 15px;
  padding-top: 15px;
  padding-inline: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  color: white;
  z-index: 5;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.footer-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.footer-tagline {
  color: rgba(134, 132, 124, 1);
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 5px;
}

.footer-copyright {
  color: var(--text-gray);
  font-size: 0.8rem;
  font-weight: 400;
}

.footer-copyright-mobile {
  color: var(--text-gray);
  font-size: 0.7rem;
  font-weight: 400;
  margin-top: 5px;
  text-align: right;
}

.footer-social-icons {
  display: flex;
  align-items: center;
  gap: 20px;
}

.footer-icon-link {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-gray);
  transition: all 0.2s ease-in-out;
}

.footer-icon-link:hover {
  color: var(--primary-color);
  transform: translateY(-2px);
}

.footer-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-icon svg {
  width: 28px;
  height: 28px;
}

/* Developer log viewer link styling */
.dev-link {
  background-color: rgba(0, 128, 255, 0.2);
  border-radius: 4px;
  padding: 2px;
}

.dev-link:hover {
  background-color: rgba(0, 128, 255, 0.4);
}

/* Mobile styles */
@media (max-width: 768px) {
  .footer-container {
    padding-inline: 15px;
    padding-bottom: 10px;
    padding-top: 10px;
  }

  .footer-tagline {
    font-size: 0.9rem;
  }

  .footer-social-icons {
    gap: 12px;
  }

  .footer-icon {
    width: 24px;
    height: 24px;
  }

  .footer-icon svg {
    width: 24px;
    height: 24px;
  }
}
