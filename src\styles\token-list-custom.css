/* Custom styles for token list to match pending transaction module */

/* Token list container - overlay */
.token-list-container {
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: fixed;
  height: 100vh;
  width: 100vw;
  z-index: 999;
  background-color: rgba(0, 0, 0, 0.7);
  opacity: 0;
}

.token-list-container

/* Token list box - match pending-box */
.token-list {
  z-index: 1000;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 0;
  border: solid 1px var(--border);
  background: var(--box-bg);
  border-radius: 25px;
  width: 90vw;
  max-width: 480px;
  height: auto;
  max-height: 80vh;
  opacity: 1;
  pointer-events: all;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

/* Token list header - match pending-header */
.token-list-top {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 12px 15px;
  background: var(--box-bg);
  position: sticky;
  top: 0;
  z-index: 2;
}

/* Header with title and close button */
.pending-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border);
  margin-bottom: 1px;
}

.pending-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text);
}

.close-button {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-gray);
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-button:hover {
  color: var(--text);
}

/* Search input container */
.inputWithIcon {
  position: relative;
  width: 100%;
}

/* Search input */
.quick-import-bar {
  width: 100%;
  padding: 8px 8px 8px 35px;
  background: var(--box-inner);
  border: 1px solid var(--border);
  border-radius: 15px;
  color: var(--text);
  font-size: 0.95rem;
  font-family: var(--f-family);
  outline: none;
  transition: all 0.2s ease;
}

.quick-import-bar:focus {
  border-color: var(--primary-color);
}

/* Search icon */
.inputIcon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-gray);
}

/* Base tokens section */
.base-tokens-section {
  width: 100%;
  margin-top: 2px;
}

.base-tokens {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: flex-start;
}

.base-item {
  display: flex;
  align-items: center;
  padding: 5px 10px;
  background: var(--box-inner);
  border: 1px solid var(--border);
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.base-item:hover {
  background: var(--box-hover);
  transform: translateY(-1px);
  border-color: var(--primary-color);
}

/* Removed bottom shadow gradient */

.base-item img {
  transition: transform 0.2s ease;
}

.base-item:hover img {
  transform: scale(1.1);
}

.base-symbol {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text);
  margin-left: 5px;
  transition: color 0.2s ease;
}

.base-item:hover .base-symbol {
  color: var(--primary-color);
}

/* Token list items */
.token-list-items {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden; /* Prevent horizontal scrollbar */
  padding: 0;
  max-height: calc(80vh - 140px);
}

/* Token list item */
.token-list-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-left: 2px solid transparent;
  position: relative;
}

.token-list-item:hover {
  background-color: var(--box-hover);
  border-left: 2px solid var(--primary-color);
  transform: translateX(1px);
  margin-right: 1px; /* Compensate for the transform to prevent scrollbar */
}

.token-list-item:hover::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--primary-color);
  opacity: 0.5;
}

.token-list-item.disable {
  opacity: 0.5;
  pointer-events: none;
}

/* Token image */
.token-list-item-image {
  margin-right: 15px;
  transition: transform 0.2s ease;
}

.token-list-item:hover .token-list-item-image {
  transform: scale(1.05);
}

/* Token info layout */
.token-list-row-sb {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.token-list-col {
  display: flex;
  flex-direction: column;
}

.token-list-col-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  transition: transform 0.2s ease;
}

.token-list-item:hover .token-list-col-right {
  transform: translateX(-1px);
}

/* Token text styles */
.token-list-item-text-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text);
  margin-bottom: 2px;
  transition: color 0.2s ease;
}

.token-list-item:hover .token-list-item-text-name {
  color: var(--primary-color);
}

.token-list-item-text-symbol {
  font-size: 0.85rem;
  color: var(--text-gray);
  transition: color 0.2s ease;
}

.token-list-item:hover .token-list-item-text-symbol {
  color: var(--text);
}

/* Mobile styles */
@media (max-width: 600px) {
  .token-list {
    width: 100%;
    height: 80vh;
    max-width: none;
    border-radius: 25px 25px 0 0;
    position: fixed;
    bottom: 0;
    top: auto;
  }

  .token-list-top {
    padding: 12px;
  }

  .token-list-items {
    max-height: calc(80vh - 130px);
  }

  .token-list-item {
    padding: 10px 12px;
  }
}
