"use client";

import React, {
  createContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  useLayoutEffect,
  use,
} from "react";
import PropTypes from "prop-types";
import { ethers } from "ethers";

import CryptoJS from "crypto-js";

import ERC20ABI from "../constants/abis/erc20.json";
import uniswapRouterAB<PERSON> from "../constants/abis/UniswapRouter.json";

import { useEthersSigner } from "./signer";
import { erc20Abi } from "viem";
import wethABI from "../constants/abis/wethABI.json";

import mergeTokens from "../constants/tokens/mergeTokens";

import fetchBlockNumber from "../services/blockchain/fetchBlockNumber";
import { toast } from "react-hot-toast";

import {
  ETH_TOKENS_DISPLAY,
  CHAINS,
  BASE_TOKENS,
} from "../constants/constants";

import { BrowserProvider } from "ethers";
import { useDisconnect } from "@reown/appkit/react";

/* import {
  useWeb3ModalProvider,
  useWeb3ModalAccount,
} from "@web3modal/ethers/react"; */
import { useAppKitProvider, useAppKitAccount } from "@reown/appkit/react";
import { useAppKitNetwork } from "@reown/appkit/react";

export const BlockchainContext = createContext({
  contractAddress: "",
  symbol: "",
  balance: 0,
  targetSymbol: "",
  tier: "",
});

async function delay() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, 1000);
  });
}
export const BlockchainProvider = ({ children }) => {
  /*   const limiter = useRef(
    new Bottleneck({
      maxConcurrent: 1,
      minTime: 1000,
    })
  );
  const limit = (task) => limiter.current.schedule(task); */
  const bannedSymbols = ["WBNB", "USD0 [www.usual.finance]", "WETH"];

  const [ETH_TOKENS, setEthTokens] = useState({});
  const [ALL_TOKENS, setAllTokens] = useState({});
  const [chain_id, setChainId] = useState(1);
  const [isNetworkChanging, setIsNetworkChanging] = useState(false);
  const networkChangeTimeoutRef = useRef(null);

  const { walletProvider } = useAppKitProvider("eip155");

  const [provider, setProvider] = useState(null);
  const [signer, setSigner] = useState(null);
  const { isConnected } = useAppKitAccount();
  const { disconnect } = useDisconnect();

  const { address: account } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();

  const [authToken, setAuthToken] = useState(null);
  const [isJuicedMode, setIsJuicedMode] = useState(false);

  useEffect(() => {
    const savedJuicedMode = localStorage.getItem("juiced_mode_enabled");
    if (savedJuicedMode === "true") {
      setIsJuicedMode(true);
      console.log("Juiced mode enabled from saved preference");
    } else {
      console.log("Juiced mode disabled by default");
    }
  }, []);

  useEffect(() => {
    if (typeof window !== "undefined") {
      console.log("Saving juiced mode preference:", isJuicedMode);
      localStorage.setItem(
        "juiced_mode_enabled",
        isJuicedMode ? "true" : "false"
      );
    }
  }, [isJuicedMode]);

  const [isCereEnabled, setIsCereEnabled] = useState(false);

  const toggleCereTracking = useCallback(
    (enabled) => {
      const newValue = typeof enabled === "boolean" ? enabled : !isCereEnabled;

      console.log(`${newValue ? "Enabling" : "Disabling"} Cere tracking state`);

      setIsCereEnabled(newValue);
    },
    [isCereEnabled]
  );

  useEffect(() => {
    if (typeof window === "undefined") return;

    const savedPreference = localStorage.getItem("cere_tracking_enabled");

    if (savedPreference === "true") {
      setIsCereEnabled(true);
      console.log("Cere tracking enabled from saved preference");
    } else {
      setIsCereEnabled(false);

      if (savedPreference === null) {
        localStorage.setItem("cere_tracking_enabled", "false");
      }
      console.log("Cere tracking disabled by default");
    }
  }, []);

  const cereEventServiceRef = useRef(null);

  useEffect(() => {
    const initializeCere = async () => {
      const cereEnabled =
        localStorage.getItem("cere_tracking_enabled") === "true";

      if (
        cereEnabled &&
        account &&
        typeof window !== "undefined" &&
        isConnected
      ) {
        try {
          if (cereEventServiceRef.current) {
            console.log("Cere EventService already initialized");

            if (cereEventServiceRef.current.isInitialized()) {
              console.log("EventService is properly initialized");
            } else {
              console.log("EventService needs to be reinitialized");
              cereEventServiceRef.current = null;
            }

            return;
          }

          console.log("Initializing Cere EventService...");

          const EventService = (await import("../services/cere/EventService"))
            .default;

          cereEventServiceRef.current = new EventService(account);
          await cereEventServiceRef.current.init();

          if (cereEventServiceRef.current.isInitialized()) {
            console.log("Cere EventService initialized successfully");

            const connectDetails = {
              account: account,
              chainId: chain_id,
            };
            console.log("Initial connect details:", connectDetails);
            const result =
              await cereEventServiceRef.current.dispatchWalletConnectEvent(
                connectDetails
              );
            console.log("Wallet connection event dispatch result:", result);

            try {
              const events = await cereEventServiceRef.current.getAllEvents();
              console.log(
                `Retrieved ${events ? events.length : 0} stored events`
              );
            } catch (eventsError) {
              console.warn(
                "Error retrieving events, but continuing:",
                eventsError
              );
            }
          } else {
            console.warn("Cere EventService initialization incomplete");
          }
        } catch (error) {
          console.warn("Failed to initialize Cere EventService:", error);
          console.warn(
            "Error details:",
            JSON.stringify(error, Object.getOwnPropertyNames(error))
          );
          cereEventServiceRef.current = null;
        }
      }
    };

    if (typeof window !== "undefined") {
      initializeCere();
    }

    return () => {
      if (cereEventServiceRef.current) {
        console.log("Cleaning up Cere EventService on unmount");
        cereEventServiceRef.current = null;
      }
    };
  }, [account, isConnected]);

  const trackWalletConnection = async () => {
    try {
      const cereEnabled =
        localStorage.getItem("cere_tracking_enabled") === "true";

      if (typeof window === "undefined" || !account || !cereEnabled) {
        if (!cereEnabled) {
          console.log(
            "Cere tracking is disabled. Enable it to track wallet connections."
          );
        }
        return;
      }

      console.log("Tracking wallet connection with Cere...");

      const urlParams = new URLSearchParams(window.location.search);
      const urlReferenceId = urlParams.get("ref");

      if (urlReferenceId) {
        console.log(
          `Found reference ID in URL: ${urlReferenceId}, storing in localStorage`
        );
        localStorage.setItem("referenceId", urlReferenceId);
      }

      const walletId = account;
      const referenceId = localStorage.getItem("referenceId");

      console.log("Wallet ID:", walletId);
      console.log("Reference ID:", referenceId || "None");

      const cereRef = account;

      if (!cereEventServiceRef.current) {
        console.log("Creating new EventService with cereRef:", cereRef);

        const EventService = (await import("../services/cere/EventService"))
          .default;

        cereEventServiceRef.current = new EventService(cereRef);

        console.log("Initializing EventService...");
        await cereEventServiceRef.current.init();

        if (!cereEventServiceRef.current.isInitialized()) {
          console.warn("EventService initialization incomplete");
          return;
        }
      }

      if (!cereEventServiceRef.current.isInitialized()) {
        console.warn("EventService not properly initialized");
        return;
      }

      console.log("Dispatching wallet connection event...");
      const connectDetails = {
        account: account,
        chainId: chain_id,
      };
      console.log("Connect details:", connectDetails);
      const result =
        await cereEventServiceRef.current.dispatchWalletConnectEvent(
          connectDetails
        );

      console.log("Wallet connection event dispatch result:", result);
      console.log("Wallet connection event dispatched to Cere");

      return result;
    } catch (error) {
      console.warn("Failed to track wallet connection with Cere:", error);
      console.warn(
        "Error details:",
        JSON.stringify(error, Object.getOwnPropertyNames(error))
      );
      return false;
    }
  };

  const trackSwapEvent = async (swapDetails) => {
    try {
      const cereEnabled =
        localStorage.getItem("cere_tracking_enabled") === "true";

      if (typeof window === "undefined" || !cereEnabled) {
        if (!cereEnabled) {
          console.log(
            "Cere tracking is disabled. Enable it to track swap events."
          );
        }
        return false;
      }

      if (account && cereEventServiceRef.current) {
        console.log("Tracking swap event with Cere...");
        console.log("Swap details:", JSON.stringify(swapDetails, null, 2));

        if (!cereEventServiceRef.current.isInitialized()) {
          console.warn("EventService not properly initialized for swap event");

          await trackWalletConnection();

          if (
            !cereEventServiceRef.current ||
            !cereEventServiceRef.current.isInitialized()
          ) {
            console.warn("Failed to reinitialize EventService for swap event");
            return false;
          }
        }

        const swapEventDetails = { ...swapDetails };

        const referenceId = localStorage.getItem("referenceId");
        if (referenceId) {
          console.log(`Including reference ID in swap event: ${referenceId}`);
          swapEventDetails.referenceId = referenceId;
        }

        if (!swapEventDetails.walletId) {
          swapEventDetails.walletId = account;
        }

        swapEventDetails.timestamp = new Date().toISOString();
        swapEventDetails.chainId = chain_id;

        console.log(
          "Enhanced swap event details:",
          JSON.stringify(swapEventDetails, null, 2)
        );

        console.log("Dispatching swap event to Cere...");
        const result = await cereEventServiceRef.current.dispatchSwapEvent(
          swapEventDetails
        );

        console.log("Swap event dispatch result:", result);
        console.log("Swap event dispatched to Cere");

        return result;
      } else if (!cereEventServiceRef.current) {
        console.warn(
          "Cere EventService not initialized. Initialize with trackWalletConnection first."
        );

        if (account) {
          console.log(
            "Attempting to initialize EventService before swap event..."
          );
          const initResult = await trackWalletConnection();

          if (initResult && cereEventServiceRef.current) {
            console.log(
              "EventService initialized successfully, now dispatching swap event"
            );

            const swapEventDetails = {
              ...swapDetails,
              walletId: account,
              timestamp: new Date().toISOString(),
              chainId: chain_id,
            };

            const referenceId = localStorage.getItem("referenceId");
            if (referenceId) {
              swapEventDetails.referenceId = referenceId;
            }

            console.log("Dispatching swap event after initialization...");
            const result = await cereEventServiceRef.current.dispatchSwapEvent(
              swapEventDetails
            );

            console.log("Swap event dispatch result:", result);
            console.log("Swap event dispatched to Cere after initialization");

            return result;
          } else {
            console.warn("Failed to initialize EventService for swap event");
            return false;
          }
        }
      }

      return false;
    } catch (error) {
      console.warn("Failed to track swap event with Cere:", error);
      console.warn(
        "Error details:",
        JSON.stringify(error, Object.getOwnPropertyNames(error))
      );
      return false;
    }
  };

  useEffect(() => {
    if (networkChangeTimeoutRef.current) {
      clearTimeout(networkChangeTimeoutRef.current);
      networkChangeTimeoutRef.current = null;
    }

    setIsNetworkChanging(true);

    let newChainId = chain_id;

    if (chainId === "eip155:1" || chainId === 1 || chainId === "1") {
      if (chain_id !== 1) {
        newChainId = 1;
        setChainId(1);
      }
    } else if (
      chainId === "eip155:8453" ||
      chainId === 8453 ||
      chainId === "8453"
    ) {
      if (chain_id !== 8453) {
        newChainId = 8453;
        setChainId(8453);
      }
    } else if (chainId === "eip155:56" || chainId === 56 || chainId === "56") {
      if (chain_id !== 56) {
        newChainId = 56;
        setChainId(56);
      }
    } else if (!chainId) {
      if (chain_id !== 1) {
        newChainId = 1;
        setChainId(1);
      }
    }

    console.log(`Network state check: ${chain_id} to ${newChainId}`);

    networkChangeTimeoutRef.current = setTimeout(() => {
      setIsNetworkChanging(false);
      console.log("Network transition complete");
    }, 1000);

    return () => {
      if (networkChangeTimeoutRef.current) {
        clearTimeout(networkChangeTimeoutRef.current);
        networkChangeTimeoutRef.current = null;
      }
    };
  }, [chainId, chain_id]);

  const authenticateCheckRef = useRef(false);

  const lastAuthenticationTime = useRef(0);

  const authenticate = async () => {
    if (!isConnected || !account) {
      console.log("Not connected or no account, skipping authentication");
      return;
    }

    const now = Date.now();
    if (now - lastAuthenticationTime.current < 30000) {
      console.log("Authentication attempted too frequently, skipping");
      return;
    }

    lastAuthenticationTime.current = now;

    try {
      console.log("Authenticating with wallet address:", account);

      const response = await fetch("/api/auth", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ walletAddress: account }),
      });

      const data = await response.json();
      if (data.token) {
        console.log("Authentication successful, received token");

        if (data.token !== authToken) {
          localStorage.setItem("authToken", data.token);
          setAuthToken(data.token);
        }
      } else {
        console.warn("Authentication failed - no token received");
      }
    } catch (error) {
      console.warn("Error authenticating:", error);
    }
  };

  useEffect(() => {
    const authenticateCheck = async () => {
      if (authenticateCheckRef.current) return;

      authenticateCheckRef.current = true;

      try {
        if (isConnected && account && !localStorage.getItem("authToken")) {
          console.log("No auth token found, authenticating...");
          await authenticate();

          const cereEnabled =
            localStorage.getItem("cere_tracking_enabled") === "true";

          if (cereEnabled) {
            console.log(
              "Cere tracking is enabled, initializing EventService during authentication..."
            );

            try {
              const result = await trackWalletConnection();
              console.log(
                "EventService initialization during authentication result:",
                result
              );

              if (cereEventServiceRef.current) {
                const isInitialized =
                  cereEventServiceRef.current.isInitialized();
                console.log(
                  "EventService initialization status:",
                  isInitialized
                );

                if (!isInitialized) {
                  console.warn(
                    "EventService not properly initialized during authentication"
                  );
                }
              }
            } catch (error) {
              console.warn(
                "Error initializing EventService during authentication:",
                error
              );
            }
          }
        } else if (isConnected && account) {
          console.log("Auth token already exists, skipping authentication");
        }
      } finally {
        authenticateCheckRef.current = false;
      }
    };

    if (isConnected && account) {
      authenticateCheck();
    }

    return () => {
      authenticateCheckRef.current = false;
    };
  }, [account, isConnected]);

  const isCheckingRef = useRef(false);

  const lastTokenVerificationTime = useRef(0);

  useEffect(() => {
    const checkTokenValidity = async () => {
      if (isCheckingRef.current) return;

      if (authenticateCheckRef.current) return;

      const now = Date.now();
      if (now - lastTokenVerificationTime.current < 30000) return;

      isCheckingRef.current = true;
      lastTokenVerificationTime.current = now;

      try {
        const token = localStorage.getItem("authToken");

        if (!token) {
          console.log("No auth token found, skipping verification");
          return;
        }

        if (token === authToken) {
          console.log("Token already set in state, skipping verification");
          return;
        }

        console.log("Verifying token...");

        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 5000);

          try {
            const response = await fetch("/api/verify-token", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              signal: controller.signal,
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
              if (response.status === 401) {
                console.log(
                  "Token verification failed with 401 status - token may be expired or invalid"
                );

                localStorage.removeItem("authToken");
                setAuthToken(null);
                return;
              } else {
                throw new Error(`HTTP error! status: ${response.status}`);
              }
            }

            const data = await response.json();
            if (data && !data.isValid && isConnected && account) {
              console.log("Token is invalid, removing");
              localStorage.removeItem("authToken");
              setAuthToken(null);

              let tokenList;
              if (chain_id === 8453) {
                tokenList = BASE_TOKENS;
              } else {
                tokenList = ETH_TOKENS_DISPLAY;
              }
              dollarRef.current = tokenList;

              disconnect();
            } else if (data && data.isValid && isConnected && account) {
              console.log("Token is valid, setting in state");
              if (token !== authToken) {
                setAuthToken(token);
              }
            }
          } catch (fetchError) {
            if (fetchError.name === "AbortError") {
              console.warn("Token verification request timed out");
            } else if (
              fetchError.message &&
              fetchError.message.includes("Cross-Origin")
            ) {
              console.warn(
                "Error checking Cross-Origin-Opener-Policy:",
                fetchError.message
              );
            } else {
              console.warn("Error during token verification:", fetchError);
            }
          } finally {
            clearTimeout(timeoutId);
          }
        } catch (error) {
          console.warn("Failed to verify token:", error);
        }
      } finally {
        isCheckingRef.current = false;
      }
    };

    if (isConnected && account) {
      checkTokenValidity();
    }

    const intervalId = setInterval(checkTokenValidity, 60000);
    return () => clearInterval(intervalId);
  }, [isConnected, account, authToken]);

  /*   useEffect(() => {
    if (selectedNetworkId) {
      if (selectedNetworkId !== chain_id) {
        if (selectedNetworkId === 8453) {
          setChainId(8453);
        } else if (selectedNetworkId === 1) {
          setChainId(1);
        } else if (selectedNetworkId === 56) {
          setChainId(56);
        }
      }
      console.log("selectedNetworkId", selectedNetworkId);
    }
  }, [selectedNetworkId]); */

  const isFetching = useRef(false);

  useEffect(() => {
    const setupTokens = async () => {
      if (isFetching.current) return;

      try {
        isFetching.current = true;

        let filteredTokens = {};

        if (chain_id === 8453) {
          Object.entries(BASE_TOKENS).forEach(([key, token]) => {
            if (token.chain_id === 8453) {
              filteredTokens[key] = { ...token };
            }
          });
        } else if (chain_id === 1) {
          Object.entries(ETH_TOKENS_DISPLAY).forEach(([key, token]) => {
            if (token.chain_id === 1) {
              filteredTokens[key] = { ...token };
            }
          });
        }

        dollarRef.current = filteredTokens;

        setEthTokens(filteredTokens);
      } catch (error) {
        console.warn("Failed to set up tokens:", error);
      } finally {
        isFetching.current = false;
      }
    };

    if (ETH_TOKENS && Object.keys(ETH_TOKENS).length === 0) {
      setupTokens();
    }

    if (ETH_TOKENS && Object.keys(ETH_TOKENS).length > 0) {
      setAllTokens(mergeTokens(chain_id, ETH_TOKENS));
    }
  }, [chain_id, ETH_TOKENS, account]);

  useEffect(() => {
    const setupProvider = async () => {
      if (walletProvider) {
        try {
          const ethersProvider = new BrowserProvider(walletProvider);

          const signer = await ethersProvider.getSigner();

          setSigner(signer);
          setProvider(ethersProvider);
        } catch (error) {
          console.warn("Error setting up provider and signer:", error);
        }
      }
    };

    setupProvider();
  }, [walletProvider, account, chain_id, isConnected]);

  const dollarRef = useRef(ALL_TOKENS);

  useEffect(() => {
    let filteredTokens = {};

    if (chain_id === 8453) {
      Object.entries(BASE_TOKENS).forEach(([key, token]) => {
        if (token.chain_id === 8453) {
          filteredTokens[key] = { ...token };
        }
      });
    } else if (chain_id === 1) {
      Object.entries(ETH_TOKENS_DISPLAY).forEach(([key, token]) => {
        if (token.chain_id === 1) {
          filteredTokens[key] = { ...token };
        }
      });
    }

    dollarRef.current = filteredTokens;

    setAllTokens(mergeTokens(chain_id, filteredTokens));
  }, [chain_id]);

  const uniswapRouterAddress = CHAINS[chain_id].uniswapRouterAddressV2;
  const wethAddress = CHAINS[chain_id].wethAddress;
  const usdcAddress = CHAINS[chain_id].usdcAddress;

  const blockNumberRef = useRef(0);
  const ethDollarPrice = useRef("");

  const isFetchingBlockNumber = useRef(false);

  const lastBlockFetchTime = useRef(0);

  const fetchNewBlockNumber = async () => {
    if (!account) {
      console.log("No account, skipping block number fetch");
      return;
    }

    const token = authToken || localStorage.getItem("authToken");

    if (!token) {
      console.log("No auth token, skipping block number fetch");
      return;
    }

    const now = Date.now();
    if (
      isFetchingBlockNumber.current ||
      now - lastBlockFetchTime.current < 10000
    ) {
      return;
    }

    isFetchingBlockNumber.current = true;

    try {
      console.log("Fetching block number...");
      const blockNumber = await fetchBlockNumber(chain_id, token);

      if (blockNumberRef.current !== blockNumber) {
        console.log(
          `Block number updated: ${blockNumberRef.current} -> ${blockNumber}`
        );
        blockNumberRef.current = blockNumber;

        const amountIn = ethers.parseEther("1");
        const path = [wethAddress, usdcAddress];

        try {
          const response = await fetch("/api/rpc-call/get-amounts-out", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              chain_id,
              amountIn: amountIn.toString(),
              path,
              uniswapRouterAddress,
            }),
          });

          if (response.ok) {
            const data = await response.json();
            const amounts = data.amounts;

            let ethPriceInUsdc = amounts[1];
            let decimals = chain_id === 56 ? 18 : 6;

            ethPriceInUsdc = ethers.formatUnits(ethPriceInUsdc, decimals);
            ethPriceInUsdc = Number(ethPriceInUsdc).toFixed(0);

            if (ethDollarPrice.current !== ethPriceInUsdc) {
              console.log(
                `ETH price updated: $${ethDollarPrice.current} -> $${ethPriceInUsdc}`
              );
              ethDollarPrice.current = ethPriceInUsdc;
            }
          }
        } catch (error) {
          console.warn("Error fetching ETH price:", error);
        }
      }
    } catch (error) {
      console.warn("Failed to fetch new block number:", error);
    } finally {
      isFetchingBlockNumber.current = false;
      lastBlockFetchTime.current = Date.now();
    }
  };
  useEffect(() => {
    if (!account) {
      return;
    }

    const token = authToken || localStorage.getItem("authToken");

    if (!token) {
      console.log("No auth token available for block number interval");
      return;
    }

    console.log("Setting up block number fetch interval");
    fetchNewBlockNumber();

    const intervalId = setInterval(fetchNewBlockNumber, 45000);

    return () => {
      console.log("Clearing block number fetch interval");
      clearInterval(intervalId);
    };
  }, [chain_id, account, authToken]);

  const savedAddedPriority = useRef("auto");
  const savedSlippage = useRef("auto");

  const savedSlippageValue = useRef(6);
  const savedInputAmount = useRef(undefined);
  const savedOutputAmount = useRef(undefined);

  const useAutoGas = useRef(true);
  const useAutoSlippage = useRef(false);
  const savedPriorityGas = useRef(30);

  function updateData(type, value) {
    if (type === "savedSlippage") {
      savedSlippage.current = value;
    } else if (type === "savedSlippageValue") {
      savedSlippageValue.current = value;
    } else if (type === "savedAddedPriority") {
      savedAddedPriority.current = value;
    } else if (type === "savedInputAmount") {
      savedInputAmount.current = Number(value);
    } else if (type === "savedOutputAmount") {
      savedOutputAmount.current = value;
    }
  }

  /*   else if (type === "useAutoGas") {
    useAutoGas.current = value;
    console.log("useAutoGas", value);
  } else if (type === "useAutoSlippage") {
    useAutoSlippage.current = value;
    console.log("useAutoSlippage", value);
  } */

  const tokenListOpenRef = useRef(false);

  const isUpdatingDollarRef = useRef(false);

  const lastUpdateTime = useRef(0);

  useEffect(() => {
    if (!account || !authToken) {
      return;
    }

    async function updateDollarRef() {
      const now = Date.now();

      if (
        isUpdatingDollarRef.current ||
        (now - lastUpdateTime.current < 15000 && !tokenListOpenRef.current)
      ) {
        return;
      }

      isUpdatingDollarRef.current = true;
      console.log("Updating dollar reference values...");

      try {
        const token = authToken || localStorage.getItem("authToken");

        if (!token) {
          console.log("No auth token available for updating dollar reference");
          return;
        }

        if (now - lastBlockFetchTime.current > 30000) {
          await fetchNewBlockNumber();
        }

        let tokenList;

        if (chain_id === 8453) {
          tokenList = BASE_TOKENS;
        } else {
          tokenList = ETH_TOKENS_DISPLAY;
        }

        const filteredTokens = Object.entries(tokenList)
          .filter(([_, token]) => token.chain_id === chain_id)
          .filter(([_, token]) => !bannedSymbols.includes(token.symbol));

        const tokensToProcess =
          tokenListOpenRef.current || lastUpdateTime.current === 0
            ? filteredTokens
            : filteredTokens.slice(0, 3);

        console.log(
          `Processing ${tokensToProcess.length} tokens for dollar value update`
        );

        const results = [];
        for (const [key, token] of tokensToProcess) {
          try {
            const balanceData = await getDollarValue(token);

            results.push({
              key,
              data: {
                dollarValue:
                  Number(balanceData?.dollarValue) < 1
                    ? ""
                    : balanceData?.dollarValue,
                balance:
                  Number(balanceData?.dollarValue) < 1
                    ? ""
                    : balanceData?.balance,
                symbol: token.symbol,
                is_partner: token.is_partner,
                chain_id: token.chain_id,
                name: token.name,
                address: token.address,
                decimals: token.decimals,
                logo_uri: token.logo_uri,
              },
            });
          } catch (error) {
            console.warn(
              `Error getting dollar value for ${token.symbol}:`,
              error
            );
            results.push({
              key,
              data: {
                ...token,
                dollarValue: "100.00",
                balance: "1.0000",
              },
            });
          }

          await new Promise((resolve) => setTimeout(resolve, 100));
        }

        const updatedDollarRef = results
          .filter((result) => result !== null)
          .reduce((acc, { key, data }) => {
            acc[key] = data;
            return acc;
          }, {});

        dollarRef.current = updatedDollarRef;
        console.log("Dollar reference values updated successfully");

        lastUpdateTime.current = Date.now();
      } catch (error) {
        console.warn("Error updating dollar reference:", error);
      } finally {
        isUpdatingDollarRef.current = false;
      }
    }

    updateDollarRef();

    const intervalId = setInterval(() => {
      const now = Date.now();

      if (tokenListOpenRef.current || now - lastUpdateTime.current > 60000) {
        console.log("Running scheduled dollar reference update");
        updateDollarRef();
      }
    }, 30000);

    console.log("Dollar reference update interval set up");

    return () => {
      console.log("Clearing dollar reference update interval");
      clearInterval(intervalId);
    };
  }, [account, chain_id, authToken]);

  const tokenBalanceCache = useRef({});

  const lastTokenFetchTime = useRef({});

  async function getDollarValue(Token) {
    let ethPrice = "00.00";
    let formattedBalance = "00.00";

    try {
      const token = authToken || localStorage.getItem("authToken");

      if (!token) {
        console.log(
          `No auth token available for getting ${Token.symbol} value`
        );
        return {
          balance: "0.0000",
          dollarValue: "0.00",
        };
      }

      const now = Date.now();
      const cacheKey = `${Token.symbol}-${account}-${chain_id}`;
      const lastFetchTime = lastTokenFetchTime.current[cacheKey] || 0;

      if (now - lastFetchTime < 60000 && tokenBalanceCache.current[cacheKey]) {
        console.log(`Using cached value for ${Token.symbol}`);
        return tokenBalanceCache.current[cacheKey];
      }

      console.log(`Fetching value for ${Token.symbol}...`);

      let oneEthInUSDC = ethDollarPrice.current;
      oneEthInUSDC = Number(oneEthInUSDC) || 2500;
      const nativeSymbol = CHAINS[chain_id].nativeSymbol;

      if (Token.symbol === nativeSymbol || Token.symbol === "WETH") {
        let balance;

        if (Token.symbol === "WETH") {
          try {
            console.log(`Fetching WETH balance for ${account}`);
            const response = await fetch("/api/rpc-call/get-weth-balance", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify({ chain_id, account, wethAddress }),
            });

            const data = await response.json();
            if (response.ok) {
              balance = data.balance;
            } else {
              console.warn("Error fetching WETH balance:", data.error);
              return {
                balance: "0.0000",
                dollarValue: "0.00",
              };
            }
          } catch (error) {
            console.warn("Failed to fetch WETH balance:", error);
            return {
              balance: "0.0000",
              dollarValue: "0.00",
            };
          }

          if (balance === 0) {
            const result = {
              balance: "0.0000",
              dollarValue: "0.00",
            };

            tokenBalanceCache.current[cacheKey] = result;
            lastTokenFetchTime.current[cacheKey] = now;

            return result;
          }
        } else if (Token.symbol === nativeSymbol) {
          try {
            console.log(`Fetching native balance for ${account}`);
            const response = await fetch("/api/rpc-call/get-balance", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify({ chain_id, account }),
            });

            const data = await response.json();
            if (response.ok) {
              balance = data.balance;
            } else {
              console.warn("Error fetching native balance:", data.error);
              return {
                balance: "0.0000",
                dollarValue: "0.00",
              };
            }
          } catch (error) {
            console.warn("Failed to fetch native balance:", error);
            return {
              balance: "0.0000",
              dollarValue: "0.00",
            };
          }
        }

        if (!balance) {
          const result = {
            balance: "0.0000",
            dollarValue: "0.00",
          };

          tokenBalanceCache.current[cacheKey] = result;
          lastTokenFetchTime.current[cacheKey] = now;

          return result;
        }

        balance = ethers.formatEther(balance);
        balance = Number(balance);
        formattedBalance = balance.toFixed(4);
        let totalValue = balance * oneEthInUSDC;
        totalValue = totalValue.toFixed(2);
        ethPrice = totalValue;
      } else {
        try {
          console.log(`Fetching token balance for ${Token.symbol}`);
          const response = await fetch("/api/rpc-call/get-token-balance", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              chain_id,
              account,
              tokenAddress: Token.address,
            }),
          });

          const data = await response.json();
          if (!response.ok) {
            console.warn(`Error fetching ${Token.symbol} balance:`, data.error);
            return {
              balance: "0.0000",
              dollarValue: "0.00",
            };
          }

          const tokenBalance = data.tokenBalance;
          if (!tokenBalance || tokenBalance === "0") {
            const result = {
              balance: "0.0000",
              dollarValue: "0.00",
            };

            tokenBalanceCache.current[cacheKey] = result;
            lastTokenFetchTime.current[cacheKey] = now;

            return result;
          }

          const path = [Token.address, wethAddress];
          const uniswapRouterAddress = CHAINS[chain_id].uniswapRouterAddressV2;

          console.log(`Fetching price for ${Token.symbol}`);
          const amountOutResponse = await fetch(
            "/api/rpc-call/get-amounts-out",
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify({
                chain_id,
                amountIn: tokenBalance,
                path,
                uniswapRouterAddress,
              }),
            }
          );

          const amountOutData = await amountOutResponse.json();
          if (!amountOutResponse.ok) {
            console.warn(
              `Error fetching ${Token.symbol} price:`,
              amountOutData.error
            );
            return {
              balance: "0.0000",
              dollarValue: "0.00",
            };
          }

          formattedBalance = ethers.formatUnits(tokenBalance, Token.decimals);
          formattedBalance = Number(formattedBalance).toFixed(4);

          const ethOut = ethers.formatEther(amountOutData.amounts[1]);
          const totalDollarValue = (Number(ethOut) * oneEthInUSDC).toFixed(2);
          ethPrice = totalDollarValue;
        } catch (error) {
          console.warn(`Error calculating ${Token.symbol} value:`, error);
          return {
            balance: "0.0000",
            dollarValue: "0.00",
          };
        }
      }

      const result = {
        balance: formattedBalance,
        dollarValue: ethPrice,
      };

      tokenBalanceCache.current[cacheKey] = result;
      lastTokenFetchTime.current[cacheKey] = now;

      return result;
    } catch (error) {
      console.warn(`Error getting ${Token.symbol} balance:`, error);
      return {
        balance: "0.0000",
        dollarValue: "0.00",
      };
    }
  }

  const saverInputAmount = useRef(0);

  return (
    <BlockchainContext.Provider
      value={{
        isJuicedMode,
        setIsJuicedMode,
        authToken,
        saverInputAmount,
        chain_id,
        dollarRef,
        savedAddedPriority,
        useAutoGas,
        useAutoSlippage,
        blockNumberRef,
        ethDollarPrice,
        savedOutputAmount,
        savedInputAmount,
        savedSlippage,
        savedSlippageValue,
        savedPriorityGas,
        updateData,
        provider,
        account,
        signer,
        tokenListOpenRef,
        ALL_TOKENS,
        ETH_TOKENS,

        trackWalletConnection,
        trackSwapEvent,

        isCereEnabled,
        toggleCereTracking,

        isJuicedMode,
        setIsJuicedMode,

        isNetworkChanging,
      }}
    >
      {children}
    </BlockchainContext.Provider>
  );
};

BlockchainProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
