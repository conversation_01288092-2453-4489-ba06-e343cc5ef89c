"use client";

import React, { useState, useEffect } from "react";
import SplashScreen from "../common/SplashScreen";
import NetworkTransitionWrapper from "../layout/NetworkTransitionWrapper";
import ToastTester from "../common/ToastTester";
import { useTheme } from "../../contexts/ThemeContext";

export default function RootLayoutClient({ children }) {
  const { themeClass } = useTheme();
  const [loading, setLoading] = useState(true);
  const [fadeStarted, setFadeStarted] = useState(false);
  const [dexMounted, setDexMounted] = useState(false);
  const minLoadTime = 2000;

  useEffect(() => {
    setDexMounted(true);

    if (
      typeof window !== "undefined" &&
      window.location.search.includes("cere=true")
    ) {
      const isCereEnabled =
        localStorage.getItem("cere_tracking_enabled") === "true";

      if (!isCereEnabled) {
        console.log("Enabling Cere tracking from URL parameter");

        localStorage.setItem("cere_tracking_enabled", "true");
        console.log("Set cere_tracking_enabled to true");

        const newUrl =
          window.location.pathname +
          window.location.search.replace(/[?&]cere=true(&|$)/, (_, p1) =>
            p1 === "&" ? "?" : ""
          );

        setTimeout(() => {
          console.log(
            `Reloading page with cere_tracking_enabled = ${localStorage.getItem(
              "cere_tracking_enabled"
            )}`
          );
          window.location.href = newUrl;
        }, 100);
        return;
      }
    }

    const timer = setTimeout(() => {
      setFadeStarted(true);

      setTimeout(() => {
        setLoading(false);
      }, 3000);
    }, minLoadTime);

    return () => clearTimeout(timer);
  }, [minLoadTime]);

  const dexContent = (
    <NetworkTransitionWrapper>{children}</NetworkTransitionWrapper>
  );

  return (
    <div className={themeClass}>
      {/* DEX content - always rendered but initially hidden by the splash screen */}
      {dexMounted && dexContent}

      {/* Splash screen - shown on top of the DEX until loading is complete */}
      {loading && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            zIndex: 9999,
          }}
        >
          <SplashScreen fadeStarted={fadeStarted} onLoadComplete={() => {}} />
        </div>
      )}

      {/* Test component for toast notifications */}
    </div>
  );
}
