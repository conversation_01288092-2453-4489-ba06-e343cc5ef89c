// Utility functions for swap operations
import { ethers } from 'ethers';

// API call wrapper with error handling
export const apiCall = async (endpoint, data, authToken) => {
  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || 'API call failed');
    }

    return result;
  } catch (error) {
    console.error(`API call to ${endpoint} failed:`, error);
    throw error;
  }
};

// Get Uniswap V2 quote
export const getAmountOutV2 = async (chainId, amountIn, path, routerAddress, authToken) => {
  try {
    const data = await apiCall('/api/rpc-call/get-amounts-out', {
      chain_id: chainId,
      amountIn: amountIn.toString(),
      path: path,
      uniswapRouterAddress: routerAddress,
    }, authToken);

    return data.amounts;
  } catch (error) {
    console.error('V2 quote failed:', error);
    throw error;
  }
};

// Get Uniswap V3 quote
export const getUniswapQuoteV3 = async (tokenIn, tokenOut, amountIn, chainId, authToken) => {
  try {
    const data = await apiCall('/api/rpc-call/get-quote-v3', {
      tokenIn,
      tokenOut,
      parsedSellAmount: amountIn.toString(),
      chain_id: chainId,
    }, authToken);

    return {
      amountOut: data.amountOut,
      fee: data.fee,
      feeIn: data.feeIn,
      feeOut: data.feeOut,
    };
  } catch (error) {
    console.error('V3 quote failed:', error);
    throw error;
  }
};

// Compare quotes and return the best one
export const getBestQuote = (v2Quote, v3Quote) => {
  if (v2Quote !== null && v3Quote !== null) {
    const v2QuoteBigInt = BigInt(v2Quote);
    const v3QuoteAmountOutBigInt = BigInt(v3Quote.amountOut);

    return v2QuoteBigInt > v3QuoteAmountOutBigInt
      ? {
          bestQuote: v2QuoteBigInt,
          isV3Only: false,
          isV2Only: true,
        }
      : {
          bestQuote: v3QuoteAmountOutBigInt,
          fee: v3Quote.fee,
          isV3Only: true,
          isV2Only: false,
        };
  } else if (v2Quote !== null) {
    return {
      bestQuote: BigInt(v2Quote),
      isV3Only: false,
      isV2Only: true,
    };
  } else if (v3Quote !== null) {
    return {
      bestQuote: BigInt(v3Quote.amountOut),
      isV3Only: true,
      isV2Only: false,
      fee: v3Quote.fee,
    };
  } else {
    return null;
  }
};

// ETH to Token quote
export const getEthToTokenQuote = async (amountIn, buyTokenAddress, chainId, wethAddress, routerAddress, authToken, isV2Only = false) => {
  let v2Quote = null;
  let v3Quote = null;

  // Get V2 quote
  try {
    const v2Path = [wethAddress, buyTokenAddress];
    const v2Amounts = await getAmountOutV2(chainId, amountIn, v2Path, routerAddress, authToken);
    v2Quote = v2Amounts[1];
    console.log('V2 Quote (ETH->Token):', v2Quote);
  } catch (error) {
    console.warn('V2 quote failed:', error);
  }

  // Get V3 quote (if not V2 only)
  if (!isV2Only) {
    try {
      v3Quote = await getUniswapQuoteV3(wethAddress, buyTokenAddress, amountIn, chainId, authToken);
      console.log('V3 Quote (ETH->Token):', v3Quote);
    } catch (error) {
      console.warn('V3 quote failed:', error);
    }
  }

  return getBestQuote(v2Quote, v3Quote);
};

// Token to ETH quote
export const getTokenToEthQuote = async (amountIn, sellTokenAddress, chainId, wethAddress, routerAddress, authToken, isV2Only = false) => {
  let v2Quote = null;
  let v3Quote = null;

  // Get V2 quote
  try {
    const v2Path = [sellTokenAddress, wethAddress];
    const v2Amounts = await getAmountOutV2(chainId, amountIn, v2Path, routerAddress, authToken);
    v2Quote = v2Amounts[1];
    console.log('V2 Quote (Token->ETH):', v2Quote);
  } catch (error) {
    console.warn('V2 quote failed:', error);
  }

  // Get V3 quote (if not V2 only)
  if (!isV2Only) {
    try {
      v3Quote = await getUniswapQuoteV3(sellTokenAddress, wethAddress, amountIn, chainId, authToken);
      console.log('V3 Quote (Token->ETH):', v3Quote);
    } catch (error) {
      console.warn('V3 quote failed:', error);
    }
  }

  return getBestQuote(v2Quote, v3Quote);
};

// Token to Token quote
export const getTokenToTokenQuote = async (
  amountIn, 
  sellTokenAddress, 
  buyTokenAddress, 
  chainId, 
  wethAddress, 
  routerAddress, 
  authToken, 
  isV2Only = false
) => {
  let v2Quote = null;
  let v3Quote = null;

  // Get V2 quote (through WETH)
  try {
    const v2Path = [sellTokenAddress, wethAddress, buyTokenAddress];
    const v2Amounts = await getAmountOutV2(chainId, amountIn, v2Path, routerAddress, authToken);
    v2Quote = v2Amounts[v2Amounts.length - 1];
    console.log('V2 Quote (Token->Token):', v2Quote);
  } catch (error) {
    console.warn('V2 quote failed:', error);
  }

  // Get V3 direct quote (if not V2 only)
  if (!isV2Only) {
    try {
      v3Quote = await getUniswapQuoteV3(sellTokenAddress, buyTokenAddress, amountIn, chainId, authToken);
      console.log('V3 Quote (Token->Token):', v3Quote);
    } catch (error) {
      console.warn('V3 direct quote failed:', error);
    }
  }

  return getBestQuote(v2Quote, v3Quote);
};

// Format amount for display
export const formatAmount = (amount, decimals, precision = 6) => {
  try {
    const formatted = ethers.formatUnits(amount, decimals);
    const num = parseFloat(formatted);
    
    if (num === 0) return "0";
    if (num < 0.000001) return "< 0.000001";
    
    return num.toFixed(precision);
  } catch (error) {
    console.error('Error formatting amount:', error);
    return "0";
  }
};

// Check if token needs approval
export const checkTokenApproval = async (tokenAddress, ownerAddress, spenderAddress, amount, signer) => {
  try {
    const erc20Abi = [
      "function allowance(address owner, address spender) view returns (uint256)"
    ];
    
    const tokenContract = new ethers.Contract(tokenAddress, erc20Abi, signer);
    const allowance = await tokenContract.allowance(ownerAddress, spenderAddress);
    
    const allowanceBigInt = BigInt(allowance);
    const amountBigInt = BigInt(amount);
    
    return allowanceBigInt < amountBigInt;
  } catch (error) {
    console.error('Error checking approval:', error);
    return true; // Assume approval needed on error
  }
};

// Get token balance
export const getTokenBalance = async (tokenAddress, ownerAddress, signer) => {
  try {
    const erc20Abi = [
      "function balanceOf(address owner) view returns (uint256)",
      "function decimals() view returns (uint8)"
    ];
    
    const tokenContract = new ethers.Contract(tokenAddress, erc20Abi, signer);
    const [balance, decimals] = await Promise.all([
      tokenContract.balanceOf(ownerAddress),
      tokenContract.decimals()
    ]);
    
    return {
      balance: balance.toString(),
      formatted: ethers.formatUnits(balance, decimals),
      decimals: decimals
    };
  } catch (error) {
    console.error('Error getting token balance:', error);
    return {
      balance: "0",
      formatted: "0",
      decimals: 18
    };
  }
};
