"use client";

import dynamic from "next/dynamic";
import RootLayoutWrapper from "../components/providers/RootLayoutWrapper";

const NoSSRWrapper = dynamic(
  () => Promise.resolve(({ children }) => children),
  {
    ssr: false,
  }
);

export default function ClientLayout({ children, fontClass }) {
  return (
    <NoSSRWrapper>
      <RootLayoutWrapper fontClass={fontClass}>{children}</RootLayoutWrapper>
    </NoSSRWrapper>
  );
}
