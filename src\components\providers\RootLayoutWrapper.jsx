"use client";

import React, { useEffect } from "react";
import { BlockchainProvider } from "../../context/BlockchainContext";
import AppKitProvider from "./AppKitProvider";
import ErrorBoundary from "./ErrorBoundary";
import ClientOnly from "./ClientOnly";
import RootLayoutClient from "./RootLayoutClient";
import { ThemeProvider } from "../../contexts/ThemeContext";

function setupGlobalErrorHandlers() {
  if (typeof window !== "undefined" && !window._globalErrorHandlersAdded) {
    window.addEventListener("error", (event) => {
      console.warn("Global error caught in RootLayoutWrapper:", event.error);

      event.preventDefault();

      return true;
    });

    window.addEventListener("unhandledrejection", (event) => {
      console.warn(
        "Unhandled promise rejection in RootLayoutWrapper:",
        event.reason
      );
    });

    const originalConsoleError = console.error;
    console.error = (...args) => {
      originalConsoleError(...args);

      const errorString = args.join(" ");
      if (
        !errorString.includes("React will try to recreate this component tree")
      ) {
      }
    };

    window._globalErrorHandlersAdded = true;
    console.log("Global error handlers set up in RootLayoutWrapper");
  }
}

/**
 * RootLayoutWrapper is a client component that wraps the application with all necessary providers.
 * This component is used in the app/layout.jsx file to ensure client-side rendering.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The components to render
 * @param {string} props.fontClass - The font class to apply to the body
 * @returns {React.ReactNode} The wrapped application
 */
export default function RootLayoutWrapper({ children, fontClass }) {
  useEffect(() => {
    setupGlobalErrorHandlers();
  }, []);

  return (
    <ClientOnly>
      <ErrorBoundary>
        <ThemeProvider>
          <AppKitProvider>
            <BlockchainProvider>
              <RootLayoutClient>{children}</RootLayoutClient>
            </BlockchainProvider>
          </AppKitProvider>
        </ThemeProvider>
      </ErrorBoundary>
    </ClientOnly>
  );
}
