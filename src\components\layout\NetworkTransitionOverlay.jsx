import React, { useState, useEffect, useContext } from "react";
import { BlockchainContext } from "../../context/BlockchainContext";
import { CHAINS } from "../../constants/constants";

const NetworkTransitionOverlay = ({ isVisible }) => {
  const [fadeOut, setFadeOut] = useState(false);
  const { chain_id } = useContext(BlockchainContext);

  const getNetworkName = (chainId) => {
    if (chainId && CHAINS[chainId]) {
      return CHAINS[chainId].name || "Unknown Network";
    }
    return "Unknown Network";
  };

  useEffect(() => {
    let timer;

    if (!isVisible) {
      setFadeOut(true);

      timer = setTimeout(() => {
        setFadeOut(false);
      }, 500);
    } else {
      setFadeOut(false);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isVisible]);

  if (!isVisible && !fadeOut) return null;

  return (
    <div className={`network-transition-overlay ${fadeOut ? "fade-out" : ""}`}>
      <div className="network-transition-content">
        <div className="network-transition-spinner"></div>
        <div className="network-transition-text">Switching Network...</div>
        <div className="network-transition-subtext">
          {getNetworkName(chain_id)}
        </div>
      </div>
    </div>
  );
};

export default NetworkTransitionOverlay;
