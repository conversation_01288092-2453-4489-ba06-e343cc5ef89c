// Custom hook for swap business logic
import { useState, useRef, useCallback, useContext } from "react";
import { ethers } from "ethers";
import { BlockchainContext } from "../../../context/BlockchainContext";
import getAmountOutV2 from "../../../services/blockchain/getAmountOutV2";
import getUniswapQuoteV3 from "../../../services/blockchain/getUniswapQuoteV3";
import { CHAINS } from "../../../constants/constants";

export const useSwapLogic = (sellToken, buyToken, ALL_TOKENS, chain_id) => {
  const { authToken, updateData } = useContext(BlockchainContext);

  // Get chain-specific constants
  const chainConfig = CHAINS[chain_id];
  const wethAddress = chainConfig?.wethAddress;
  const uniswapRouterAddress = chainConfig?.uniswapRouterAddressV2;
  const nativeSymbol = chainConfig?.nativeSymbol;
  // Core state
  const [sellAmount, setSellAmount] = useState(0);
  const [buyAmount, setBuyAmount] = useState("");
  const [swapData, setSwapData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isApprovalNeeded, setIsApprovalNeeded] = useState(false);

  // Refs for transaction state
  const inSwap = useRef(false);

  // Real quote fetching logic based on original QuoteView
  const fetchQuote = useCallback(async () => {
    if (
      !ALL_TOKENS[sellToken] ||
      !ALL_TOKENS[buyToken] ||
      !sellAmount ||
      sellAmount === 0
    ) {
      setSwapData(null);
      setBuyAmount("0");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const sellTokenData = ALL_TOKENS[sellToken];
      const buyTokenData = ALL_TOKENS[buyToken];
      const sellTokenDecimals = sellTokenData?.decimals;

      // Validate and clean the sell amount
      let cleanSellAmount = sellAmount.toString();

      // Check if amount is too small or invalid
      if (
        !cleanSellAmount ||
        cleanSellAmount === "0" ||
        parseFloat(cleanSellAmount) <= 0
      ) {
        console.log("❌ Invalid sell amount:", cleanSellAmount);
        setBuyAmount("0");
        setSwapData(null);
        setLoading(false);
        return;
      }

      // Limit decimal places to prevent underflow
      const maxDecimals = Math.min(sellTokenDecimals, 18);
      const sellAmountFloat = parseFloat(cleanSellAmount);
      const limitedSellAmount = sellAmountFloat.toFixed(maxDecimals);

      console.log("🔄 Processing amount:", {
        original: cleanSellAmount,
        limited: limitedSellAmount,
        decimals: sellTokenDecimals,
        maxDecimals,
      });

      const parsedSellAmount = ethers.parseUnits(
        limitedSellAmount,
        sellTokenDecimals
      );
      console.log("➡️➡️ parsedSellAmount", parsedSellAmount);

      // Determine swap type
      const defineSwapType = () => {
        const buyTokenSymbol = buyTokenData.symbol;
        const sellTokenSymbol = sellTokenData.symbol;
        if (
          sellTokenSymbol === nativeSymbol &&
          buyTokenSymbol !== nativeSymbol
        ) {
          return "ETH/TOKEN";
        } else if (
          sellTokenSymbol !== nativeSymbol &&
          buyTokenSymbol === nativeSymbol
        ) {
          return "TOKEN/ETH";
        } else {
          return "TOKEN/TOKEN";
        }
      };

      const swapType = defineSwapType();
      console.log("✅✅✅ swapType:", swapType);

      let bestQuote = null;
      let isV2Only = false;
      let isV3Only = false;
      let fee = null;

      // Check if tokens are V2 only
      const v2Only = buyTokenData.is_v2 || sellTokenData.is_v2;
      console.log("✅✅✅ v2Only:", v2Only);

      // Get quotes based on swap type
      if (swapType === "ETH/TOKEN") {
        bestQuote = await getEthToTokenBestQuote(
          parsedSellAmount,
          buyTokenData,
          v2Only
        );
      } else if (swapType === "TOKEN/ETH") {
        bestQuote = await getTokenToEthBestQuote(
          parsedSellAmount,
          sellTokenData,
          v2Only
        );
      } else {
        bestQuote = await getTokenToTokenBestQuote(
          parsedSellAmount,
          sellTokenData,
          buyTokenData,
          v2Only
        );
      }

      if (bestQuote) {
        // Format and set the buy amount
        const formattedBuyAmount = ethers.formatUnits(
          bestQuote.bestQuote,
          buyTokenData.decimals
        );
        const displayBuyAmount = parseFloat(formattedBuyAmount).toFixed(8);

        console.log(
          "💰 Quote calculated - sellAmount:",
          sellAmount,
          "buyAmount:",
          displayBuyAmount
        );
        setBuyAmount(displayBuyAmount);
        updateData("savedOutputAmount", displayBuyAmount);

        // Create swap data object
        const newSwapData = {
          ...bestQuote,
          sellToken: sellTokenData,
          buyToken: buyTokenData,
          sellAmount: parsedSellAmount,
          buyAmount: bestQuote.bestQuote,
          swapType,
          amountOut: bestQuote.bestQuote,
        };

        setSwapData(newSwapData);

        // Check if approval is needed
        if (sellTokenData.symbol === nativeSymbol) {
          setIsApprovalNeeded(false);
        } else {
          setIsApprovalNeeded(true);
        }
      } else {
        console.warn("No quotes available");
        setBuyAmount("0");
        setSwapData(null);
      }
    } catch (err) {
      console.warn("Quote fetch failed:", err.message);
      // Don't set error to prevent on-screen error messages
      // setError(err.message);
      setSwapData(null);
      setBuyAmount("0");
    } finally {
      setLoading(false);
    }
  }, [
    sellAmount,
    sellToken,
    buyToken,
    ALL_TOKENS,
    chain_id,
    authToken,
    wethAddress,
    uniswapRouterAddress,
    nativeSymbol,
    updateData,
  ]);

  // Helper functions for getting quotes
  const getEthToTokenBestQuote = async (
    parsedSellAmount,
    buyTokenData,
    v2Only
  ) => {
    let v2Quote = null;
    let v3Quote = null;

    try {
      // Get V2 quote
      const apiPath = [wethAddress, buyTokenData.address];
      const response = await fetch("/api/rpc-call/get-amounts-out", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          chain_id,
          amountIn: parsedSellAmount.toString(),
          path: apiPath,
          uniswapRouterAddress,
        }),
      });

      const data = await response.json();
      if (response.ok) {
        v2Quote = data.amounts[1];
        console.log("V2 Quote:", v2Quote);
      } else {
        console.warn("Error with V2 quote:", data.error);
      }
    } catch (error) {
      console.warn("Error fetching V2 quote:", error);
    }

    try {
      // Get V3 quote if not V2 only
      if (!v2Only) {
        v3Quote = await getUniswapQuoteV3(
          wethAddress,
          buyTokenData.address,
          parsedSellAmount,
          chain_id,
          authToken
        );
        console.log("V3 Quote:", v3Quote);
      }
    } catch (error) {
      console.warn("Fetching V3 quote failed:", error);
    }

    // Return best quote
    if (v2Quote !== null && v3Quote !== null) {
      const v2QuoteBigInt = BigInt(v2Quote);
      const v3QuoteAmountOutBigInt = BigInt(v3Quote.amountOut);

      return v2QuoteBigInt > v3QuoteAmountOutBigInt
        ? { bestQuote: v2QuoteBigInt, isV2Only: true, isV3Only: false }
        : {
            bestQuote: v3QuoteAmountOutBigInt,
            isV2Only: false,
            isV3Only: true,
            fee: v3Quote.fee,
          };
    } else if (v2Quote !== null) {
      return { bestQuote: BigInt(v2Quote), isV2Only: true, isV3Only: false };
    } else if (v3Quote !== null) {
      return {
        bestQuote: BigInt(v3Quote.amountOut),
        isV2Only: false,
        isV3Only: true,
        fee: v3Quote.fee,
      };
    } else {
      console.warn("No quotes available for ETH to Token swap.");
      return null;
    }
  };

  const getTokenToEthBestQuote = async (
    parsedSellAmount,
    sellTokenData,
    v2Only
  ) => {
    let v2Quote = null;
    let v3Quote = null;

    try {
      // Get V2 quote
      const apiPath = [sellTokenData.address, wethAddress];
      v2Quote = await getAmountOutV2(
        chain_id,
        parsedSellAmount,
        apiPath,
        uniswapRouterAddress,
        authToken
      );
      v2Quote = v2Quote[1];
      console.log("V2 Quote:", v2Quote);
    } catch (error) {
      console.warn("Fetching V2 quote failed:", error);
    }

    try {
      // Get V3 quote if not V2 only
      if (!v2Only) {
        v3Quote = await getUniswapQuoteV3(
          sellTokenData.address,
          wethAddress,
          parsedSellAmount,
          chain_id,
          authToken
        );
        console.log("V3 Quote:", v3Quote);
      }
    } catch (error) {
      console.warn("Fetching V3 quote failed:", error);
    }

    // Return best quote
    if (v2Quote !== null && v3Quote !== null) {
      const v2QuoteBigInt = BigInt(v2Quote);
      const v3QuoteAmountOutBigInt = BigInt(v3Quote.amountOut);

      return v2QuoteBigInt > v3QuoteAmountOutBigInt
        ? { bestQuote: v2QuoteBigInt, isV2Only: true, isV3Only: false }
        : {
            bestQuote: v3QuoteAmountOutBigInt,
            isV2Only: false,
            isV3Only: true,
            fee: v3Quote.fee,
          };
    } else if (v2Quote !== null) {
      return { bestQuote: BigInt(v2Quote), isV2Only: true, isV3Only: false };
    } else if (v3Quote !== null) {
      return {
        bestQuote: BigInt(v3Quote.amountOut),
        isV2Only: false,
        isV3Only: true,
        fee: v3Quote.fee,
      };
    } else {
      console.warn("No valid quotes were found for Token to ETH swap.");
      return null;
    }
  };

  const getTokenToTokenBestQuote = async (
    parsedSellAmount,
    sellTokenData,
    buyTokenData,
    v2Only
  ) => {
    let v2Quote = null;
    let v3Quote = null;

    try {
      // Get V2 quote
      const pathV2 = [sellTokenData.address, wethAddress, buyTokenData.address];
      v2Quote = await getAmountOutV2(
        chain_id,
        parsedSellAmount,
        pathV2,
        uniswapRouterAddress,
        authToken
      );
      v2Quote = v2Quote[v2Quote.length - 1];
      console.log("V2 Quote:", v2Quote);
    } catch (error) {
      console.warn("Error fetching V2 quote:", error);
    }

    try {
      // Get V3 quote if not V2 only
      if (!v2Only && !buyTokenData.force_v2 && !sellTokenData.force_v2) {
        v3Quote = await getUniswapQuoteV3(
          sellTokenData.address,
          buyTokenData.address,
          parsedSellAmount,
          chain_id,
          authToken
        );
        console.log("V3 Direct Quote:", v3Quote);
      }
    } catch (error) {
      console.warn("Error fetching V3 direct quote:", error);
    }

    // Return best quote
    if (v2Quote !== null && v3Quote !== null) {
      const v2QuoteBigInt = BigInt(v2Quote);
      const v3QuoteAmountOutBigInt = BigInt(v3Quote.amountOut);

      return v2QuoteBigInt > v3QuoteAmountOutBigInt
        ? { bestQuote: v2QuoteBigInt, isV2Only: true, isV3Only: false }
        : {
            bestQuote: v3QuoteAmountOutBigInt,
            isV2Only: false,
            isV3Only: true,
            fee: v3Quote.fee,
          };
    } else if (v2Quote !== null) {
      return { bestQuote: BigInt(v2Quote), isV2Only: true, isV3Only: false };
    } else if (v3Quote !== null) {
      return {
        bestQuote: BigInt(v3Quote.amountOut),
        isV2Only: false,
        isV3Only: true,
        fee: v3Quote.fee,
      };
    } else {
      console.warn("No valid quotes were found for Token to Token swap.");
      return null;
    }
  };

  return {
    // State
    sellAmount,
    setSellAmount,
    buyAmount,
    setBuyAmount,
    swapData,
    loading,
    error,
    isApprovalNeeded,

    // Actions
    fetchQuote,

    // Refs
    inSwap,
  };
};
