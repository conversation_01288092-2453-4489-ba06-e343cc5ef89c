import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";
import wethABI from "../../../../constants/abis/wethABI.json";

const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }

    const authenticatedAddress = authResult.address;

    const body = await request.json();
    const { chain_id, account, wethAddress } = body;

    if (!chain_id || !RPC_URLS[chain_id] || !account || !wethAddress) {
      return NextResponse.json(
        { error: "Invalid or missing parameters" },
        { status: 400 }
      );
    }

    const provider = new ethers.JsonRpcProvider(RPC_URLS[chain_id]);

    const wethContract = new ethers.Contract(wethAddress, wethABI, provider);

    const balance = await wethContract.balanceOf(account);

    return NextResponse.json({ balance: balance.toString() });
  } catch (error) {
    console.warn("Error fetching WETH balance:", error);
    return NextResponse.json(
      { error: "Failed to fetch WETH balance" },
      { status: 500 }
    );
  }
}
