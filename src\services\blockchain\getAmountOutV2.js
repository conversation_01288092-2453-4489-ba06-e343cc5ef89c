async function getAmountOutV2(
  chain_id,
  amountIn,
  path,
  uniswapRouterAddress,
  authToken
) {
  try {
    if (!amountIn || !path || !uniswapRouterAddress) {
      return null;
    }

    const response = await fetch("/api/rpc-call/get-amounts-out", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify({
        chain_id,
        amountIn: amountIn.toString(),
        path,
        uniswapRouterAddress,
      }),
    });

    const data = await response.json();

    if (response.ok) {
      console.log("Swap amounts:", data.amounts);
      return data.amounts;
    } else {
    }
  } catch (error) {
    return null;
  }
}
export default getAmountOutV2;
