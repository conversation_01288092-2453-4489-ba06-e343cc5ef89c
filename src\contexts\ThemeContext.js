"use client";

import React, { createContext, useState, useContext, useEffect } from "react";

// Create the context
const ThemeContext = createContext();

// Create a provider component
export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(true);
  const isLightMode = !isDarkMode;

  useEffect(() => {
    // Check if there's a saved theme preference in localStorage
    const savedTheme = localStorage.getItem("theme");
    if (savedTheme) {
      setIsDarkMode(savedTheme === "dark");
    } else {
      // Default to dark mode if no preference is saved
      setIsDarkMode(true);
      localStorage.setItem("theme", "dark");
    }
  }, []);

  // Function to toggle theme
  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    localStorage.setItem("theme", newMode ? "dark" : "light");
  };

  // Helper function to conditionally apply theme-specific classes
  const getThemeClass = (
    baseClass,
    lightSuffix = "-light",
    darkSuffix = "-dark"
  ) => {
    const suffix = isDarkMode ? darkSuffix : lightSuffix;
    return `${baseClass}${suffix}`;
  };

  // Helper function to conditionally apply classes based on theme
  const applyThemeClass = (baseClass, lightClass, darkClass) => {
    return isDarkMode
      ? `${baseClass} ${darkClass}`
      : `${baseClass} ${lightClass}`;
  };

  // Helper function to get theme-specific styles
  const getThemeStyles = (darkStyles, lightStyles) => {
    return isDarkMode ? darkStyles : lightStyles;
  };

  // Simple theme class name
  const themeClass = isLightMode ? "light" : "dark";

  return (
    <ThemeContext.Provider
      value={{
        isDarkMode,
        isLightMode,
        toggleTheme,
        getThemeClass,
        applyThemeClass,
        getThemeStyles,
        theme: isDarkMode ? "dark" : "light",
        themeClass, // Add the simple theme class
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};

export default ThemeContext;
