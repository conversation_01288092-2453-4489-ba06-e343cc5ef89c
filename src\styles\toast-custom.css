/* Custom styles for toast notifications to match pending transaction style */

/* Toast container */
.Toastify__toast-container {
  width: 380px;
}

/* Toast styling - clean and minimal */
.Toastify__toast {
  background: linear-gradient(95deg, #262a2f 0%, #181a1d 100%);
  color: #ffffff;
  border-radius: 20px;
  padding: 16px 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  border: 1px solid #2a2d31;
  min-height: 80px;
  margin-bottom: 16px;
  font-family: Basel, -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI",
    Roboto, Helvetica, Arial, sans-serif;
}

/* Toast body */
.Toastify__toast-body {
  padding: 4px 0;
  font-size: 1.2rem;
  line-height: 1.5;
  font-weight: 500;
  align-items: center;
}

/* Custom close button */
.close-button {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  line-height: 1;
  margin-left: 12px;
  font-weight: 300;
}

.close-button:hover {
  color: #ffffff;
  transform: scale(1.1);
}

/* Progress bar */
.Toastify__progress-bar {
  height: 3px;
  background: linear-gradient(to right, #fce025, #e6c700);
  opacity: 0.7;
}

/* Toast icons for different types */
.Toastify__toast--success .Toastify__toast-icon {
  color: #fce025;
}

.Toastify__toast--error .Toastify__toast-icon {
  color: #ff4e4e;
}

.Toastify__toast--info .Toastify__toast-icon {
  color: #3498db;
}

.Toastify__toast--warning .Toastify__toast-icon {
  color: #f1c40f;
}

/* Custom toast body class */
.custom-toast-body {
  display: flex;
  align-items: center;
}

/* Mobile styles */
@media only screen and (max-width: 480px) {
  .Toastify__toast-container {
    width: calc(100% - 24px);
    padding: 0;
    left: 12px;
    right: 12px;
    margin: 0;
  }

  .Toastify__toast {
    margin-bottom: 8px;
  }
}
