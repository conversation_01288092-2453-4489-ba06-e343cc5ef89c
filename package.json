{"name": "pineapple-dex", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@cere-activity-sdk/ciphers": "^0.1.7", "@cere-activity-sdk/events": "^0.1.7", "@cere-activity-sdk/signers": "^0.1.7", "@cere/embed-wallet": "^0.23.4", "@reown/appkit": "^1.7.5", "@reown/appkit-adapter-ethers": "^1.7.5", "@reown/appkit-controllers": "^1.7.5", "bignumber.js": "^9.3.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "ethers": "^6.11.1", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "framer-motion": "^11.0.8", "jsonwebtoken": "^9.0.2", "next": "^15.3.2", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "react-switch": "^7.1.0", "uuid": "^11.1.0", "viem": "^2.7.15"}, "devDependencies": {"@types/node": "22.15.1", "@types/react": "^18", "@types/react-dom": "^18", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "^15.3.2", "typescript": "^5"}, "engines": {"node": "22.15.1"}}