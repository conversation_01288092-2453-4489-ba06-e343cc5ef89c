"use client";
import { useTheme } from "../../contexts/ThemeContext";

export const SVGMAIN = () => (
  <svg className="sprite" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
      <linearGradient id="grd-svg-2">
        <stop className="stop3" offset="0%"></stop>
        <stop className="stop4" offset="100%"></stop>
      </linearGradient>
    </defs>
    <defs>
      <g id="i-check">
        <path
          fill="url(#grd-svg)"
          d="M127.526 15.294L45.665 78.216.863 42.861 0 59.255l44.479 53.862 83.932-81.451z"
        ></path>
      </g>
      <g id="i-check-2">
        <path
          fill="url(#grd-svg-2)"
          d="M127.526 15.294L45.665 78.216.863 42.861 0 59.255l44.479 53.862 83.932-81.451z"
        ></path>
      </g>
      <g id="i-left">
        <path d="M207.093 30.187L176.907 0l-128 128 128 128 30.186-30.187L109.28 128z"></path>
      </g>
      <g id="i-right">
        <path d="M79.093 0L48.907 30.187 146.72 128l-97.813 97.813L79.093 256l128-128z"></path>
      </g>
      <g id="i-telegram">
        <path
          fill="url(#grd-svg)"
          d="M2.617 15.832l6.44 2.187 15.29-9.348c.222-.136.449.165.258.341L13.03 19.668l-.43 5.965c-.033.454.514.706.838.387l3.564-3.505 6.516 4.932a1.21 1.21 0 0 0 1.909-.703l4.537-20.6c.259-1.175-.893-2.167-2.016-1.736L2.585 14.12c-.796.305-.774 1.438.033 1.712z"
        ></path>
      </g>
      <g id="i-twitter">
        <path
          fill="url(#grd-svg)"
          d="m89.9 25.2c-3 1.3-6.1 2.2-9.4 2.6 3.4-2 6-5.2 7.2-9.1-3.2 1.9-6.7 3.2-10.4 4-3-3.2-7.3-5.2-12-5.2-9.1 0-16.4 7.4-16.4 16.4 0 1.3.1 2.5.4 3.7-13.6-.6-25.6-7.2-33.7-17.1-5.8 10.4.7 19 5 21.9-2.6 0-5.2-.8-7.4-2 0 8.1 5.7 14.8 13.1 16.3-1.6.5-5.2.8-7.4.3 2.1 6.5 8.2 11.3 15.3 11.4-5.6 4.4-13.8 7.9-24.3 6.8 7.3 4.7 15.9 7.4 25.2 7.4 30.2 0 46.6-25 46.6-46.6 0-.7 0-1.4-.1-2.1 3.4-2.5 6.2-5.4 8.3-8.7z"
        ></path>
      </g>
      <g id="i-medium">
        <path
          fill="url(#grd-svg)"
          d="M285.6 256c0 72.3-58.3 131-130.1 131S25.3 328.3 25.3 256s58.3-131 130.1-131c71.9 0 130.2 58.7 130.2 131m142.7 0c0 68.1-29.1 123.3-65.1 123.3s-65.1-55.2-65.1-123.3 29.1-123.3 65.1-123.3 65.1 55.2 65.1 123.3m58.4 0c0 61-10.2 110.5-22.9 110.5-12.6 0-22.9-49.5-22.9-110.5s10.2-110.5 22.9-110.5c12.6 0 22.9 49.5 22.9 110.5"
        ></path>
      </g>
      <g id="i-youtube">
        <path
          fill="url(#grd-svg)"
          d="M82.287 45.907c-.937-4.071-4.267-7.074-8.275-7.521-9.489-1.06-19.098-1.065-28.66-1.06-9.566-.005-19.173 0-28.665 1.06-4.006.448-7.334 3.451-8.27 7.521-1.334 5.797-1.35 12.125-1.35 18.094s0 12.296 1.334 18.093c.936 4.07 4.264 7.073 8.272 7.521 9.49 1.061 19.097 1.065 28.662 1.061 9.566.005 19.171 0 28.664-1.061 4.006-.448 7.336-3.451 8.272-7.521 1.333-5.797 1.34-12.124 1.34-18.093s.009-12.297-1.324-18.094zM28.9 50.4h-5.54v29.438h-5.146V50.4h-5.439v-4.822H28.9zm13.977 29.439h-4.629v-2.785c-1.839 2.108-3.585 3.136-5.286 3.136-1.491 0-2.517-.604-2.98-1.897-.252-.772-.408-1.994-.408-3.796V54.311h4.625v18.795c0 1.084 0 1.647.042 1.799.111.718.462 1.082 1.082 1.082.928 0 1.898-.715 2.924-2.166v-19.51h4.629zm17.573-7.662c0 2.361-.159 4.062-.468 5.144-.618 1.899-1.855 2.869-3.695 2.869-1.646 0-3.234-.914-4.781-2.824v2.474h-4.625V45.578h4.625v11.189c1.494-1.839 3.08-2.769 4.781-2.769 1.84 0 3.078.969 3.695 2.88.311 1.027.468 2.715.468 5.132zm17.457-4.259h-9.251v4.525c0 2.363.773 3.543 2.363 3.543 1.139 0 1.802-.619 2.066-1.855.043-.251.104-1.279.104-3.134h4.719v.675c0 1.491-.057 2.518-.099 2.98a6.59 6.59 0 0 1-1.08 2.771c-1.281 1.854-3.179 2.768-5.595 2.768s-4.262-.871-5.599-2.614c-.981-1.278-1.485-3.29-1.485-6.003v-8.941c0-2.729.447-4.725 1.43-6.015 1.336-1.747 3.177-2.617 5.54-2.617 2.321 0 4.161.87 5.457 2.617.969 1.29 1.432 3.286 1.432 6.015v5.285zm-6.929-9.755c-1.546 0-2.321 1.181-2.321 3.541v2.362h4.625v-2.362c-.001-2.36-.774-3.541-2.304-3.541zm-17.166 0c-.762 0-1.534.36-2.307 1.125v15.559c.772.774 1.545 1.14 2.307 1.14 1.334 0 2.012-1.14 2.012-3.445V61.646c0-2.302-.678-3.483-2.012-3.483zm2.584-23.19c1.705 0 3.479-1.036 5.34-3.168v2.814h4.675V8.82h-4.675v19.718c-1.036 1.464-2.018 2.188-2.953 2.188-.626 0-.994-.37-1.096-1.095-.057-.153-.057-.722-.057-1.817V8.82h-4.66v20.4c0 1.822.156 3.055.414 3.836.47 1.307 1.507 1.917 3.012 1.917zM23.851 20.598v14.021h5.184V20.598L35.271 0h-5.242l-3.537 13.595L22.812 0h-5.455l3.323 9.646c1.663 4.828 2.701 8.468 3.171 10.952zm18.368 14.375c2.342 0 4.162-.881 5.453-2.641.981-1.291 1.451-3.325 1.451-6.067v-9.034c0-2.758-.469-4.774-1.451-6.077-1.291-1.765-3.11-2.646-5.453-2.646-2.33 0-4.149.881-5.443 2.646-.993 1.303-1.463 3.319-1.463 6.077v9.034c0 2.742.47 4.776 1.463 6.067 1.293 1.76 3.113 2.641 5.443 2.641zm-2.231-18.679c0-2.387.724-3.577 2.231-3.577s2.229 1.189 2.229 3.577v10.852c0 2.387-.722 3.581-2.229 3.581s-2.231-1.194-2.231-3.581z"
        ></path>
      </g>
      <g id="i-linkedin">
        <path
          fill="url(#grd-svg)"
          d="M140.9 140.9V93.3c0-23.4-5-41.2-32.3-41.2-13.1 0-21.9 7.1-25.5 14h-.3V54.2H57v86.7h27V98c0-11.4 2.1-22.2 16.2-22.2s14 13 14 23V141zM13.2 54.2h26.9v86.7H13.2zM26.7 11C18.1 11 11 18 11 26.6s7 15.7 15.6 15.7 15.7-7 15.7-15.6v-.2c0-8.5-7-15.5-15.6-15.5z"
        ></path>
      </g>
      <g id="i-x">
        <path
          fill="url(#grd-svg)"
          d="M18.205 2.25h3.308l-7.227 8.26l8.502 11.24H16.13l-5.214-6.817L4.95 21.75H1.64l7.73-8.835L1.215 2.25H8.04l4.713 6.231zm-1.161 17.52h1.833L7.045 4.126H5.078z"
        ></path>
      </g>
    </defs>
  </svg>
);

export const CheckIcon = () => (
  <svg
    viewBox="0 0 128 128"
    xmlns="http://www.w3.org/2000/svg"
    width="30"
    height="30" // Adjust these values to reduce the size
  >
    {" "}
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <defs>
      <g id="i-check">
        <path
          fill="url(#grd-svg)"
          d="M127.526 15.294L45.665 78.216.863 42.861 0 59.255l44.479 53.862 83.932-81.451z"
        ></path>
      </g>
    </defs>
    <use xlinkHref="#i-check"></use>
  </svg>
);
export const LinkedIn = () => (
  <svg viewBox="0 0 186 186" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <defs>
      <g id="i-linkedin">
        <path
          fill="url(#grd-svg)"
          d="M140.9 140.9V93.3c0-23.4-5-41.2-32.3-41.2-13.1 0-21.9 7.1-25.5 14h-.3V54.2H57v86.7h27V98c0-11.4 2.1-22.2 16.2-22.2s14 13 14 23V141zM13.2 54.2h26.9v86.7H13.2zM26.7 11C18.1 11 11 18 11 26.6s7 15.7 15.6 15.7 15.7-7 15.7-15.6v-.2c0-8.5-7-15.5-15.6-15.5z"
        ></path>
      </g>
    </defs>
    <use xlinkHref="#i-linkedin"></use>
  </svg>
);
export const Medium = () => (
  <svg viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <defs>
      <g id="i-medium">
        <path
          fill="url(#grd-svg)"
          d="M285.6 256c0 72.3-58.3 131-130.1 131S25.3 328.3 25.3 256s58.3-131 130.1-131c71.9 0 130.2 58.7 130.2 131m142.7 0c0 68.1-29.1 123.3-65.1 123.3s-65.1-55.2-65.1-123.3 29.1-123.3 65.1-123.3 65.1 55.2 65.1 123.3m58.4 0c0 61-10.2 110.5-22.9 110.5-12.6 0-22.9-49.5-22.9-110.5s10.2-110.5 22.9-110.5c12.6 0 22.9 49.5 22.9 110.5"
        ></path>
      </g>
    </defs>
    <use xlinkHref="#i-medium"></use>
  </svg>
);

export const YouTube = () => (
  <svg viewBox="0 0 90.677 90.677" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <defs>
      <g id="i-youtube">
        <path
          fill="url(#grd-svg)"
          d="M82.287 45.907c-.937-4.071-4.267-7.074-8.275-7.521-9.489-1.06-19.098-1.065-28.66-1.06-9.566-.005-19.173 0-28.665 1.06-4.006.448-7.334 3.451-8.27 7.521-1.334 5.797-1.35 12.125-1.35 18.094s0 12.296 1.334 18.093c.936 4.07 4.264 7.073 8.272 7.521 9.49 1.061 19.097 1.065 28.662 1.061 9.566.005 19.171 0 28.664-1.061 4.006-.448 7.336-3.451 8.272-7.521 1.333-5.797 1.34-12.124 1.34-18.093s.009-12.297-1.324-18.094zM28.9 50.4h-5.54v29.438h-5.146V50.4h-5.439v-4.822H28.9zm13.977 29.439h-4.629v-2.785c-1.839 2.108-3.585 3.136-5.286 3.136-1.491 0-2.517-.604-2.98-1.897-.252-.772-.408-1.994-.408-3.796V54.311h4.625v18.795c0 1.084 0 1.647.042 1.799.111.718.462 1.082 1.082 1.082.928 0 1.898-.715 2.924-2.166v-19.51h4.629zm17.573-7.662c0 2.361-.159 4.062-.468 5.144-.618 1.899-1.855 2.869-3.695 2.869-1.646 0-3.234-.914-4.781-2.824v2.474h-4.625V45.578h4.625v11.189c1.494-1.839 3.08-2.769 4.781-2.769 1.84 0 3.078.969 3.695 2.88.311 1.027.468 2.715.468 5.132zm17.457-4.259h-9.251v4.525c0 2.363.773 3.543 2.363 3.543 1.139 0 1.802-.619 2.066-1.855.043-.251.104-1.279.104-3.134h4.719v.675c0 1.491-.057 2.518-.099 2.98a6.59 6.59 0 0 1-1.08 2.771c-1.281 1.854-3.179 2.768-5.595 2.768s-4.262-.871-5.599-2.614c-.981-1.278-1.485-3.29-1.485-6.003v-8.941c0-2.729.447-4.725 1.43-6.015 1.336-1.747 3.177-2.617 5.54-2.617 2.321 0 4.161.87 5.457 2.617.969 1.29 1.432 3.286 1.432 6.015v5.285zm-6.929-9.755c-1.546 0-2.321 1.181-2.321 3.541v2.362h4.625v-2.362c-.001-2.36-.774-3.541-2.304-3.541zm-17.166 0c-.762 0-1.534.36-2.307 1.125v15.559c.772.774 1.545 1.14 2.307 1.14 1.334 0 2.012-1.14 2.012-3.445V61.646c0-2.302-.678-3.483-2.012-3.483zm2.584-23.19c1.705 0 3.479-1.036 5.34-3.168v2.814h4.675V8.82h-4.675v19.718c-1.036 1.464-2.018 2.188-2.953 2.188-.626 0-.994-.37-1.096-1.095-.057-.153-.057-.722-.057-1.817V8.82h-4.66v20.4c0 1.822.156 3.055.414 3.836.47 1.307 1.507 1.917 3.012 1.917zM23.851 20.598v14.021h5.184V20.598L35.271 0h-5.242l-3.537 13.595L22.812 0h-5.455l3.323 9.646c1.663 4.828 2.701 8.468 3.171 10.952zm18.368 14.375c2.342 0 4.162-.881 5.453-2.641.981-1.291 1.451-3.325 1.451-6.067v-9.034c0-2.758-.469-4.774-1.451-6.077-1.291-1.765-3.11-2.646-5.453-2.646-2.33 0-4.149.881-5.443 2.646-.993 1.303-1.463 3.319-1.463 6.077v9.034c0 2.742.47 4.776 1.463 6.067 1.293 1.76 3.113 2.641 5.443 2.641zm-2.231-18.679c0-2.387.724-3.577 2.231-3.577s2.229 1.189 2.229 3.577v10.852c0 2.387-.722 3.581-2.229 3.581s-2.231-1.194-2.231-3.581z"
        ></path>
      </g>
    </defs>
    <use xlinkHref="#i-youtube"></use>
  </svg>
);
export const Telegram = () => (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <path
      fill="url(#grd-svg)"
      d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM16.64 8.8C16.49 10.38 15.84 14.22 15.51 15.99C15.37 16.74 15.09 16.99 14.83 17.02C14.25 17.07 13.81 16.64 13.25 16.27C12.37 15.69 11.87 15.33 11.02 14.77C10.03 14.12 10.67 13.76 11.24 13.18C11.39 13.03 13.95 10.7 14 10.49C14.0069 10.4476 14.0003 10.4046 13.9808 10.3662C13.9614 10.3278 13.9299 10.2959 13.89 10.275C13.83 10.25 13.75 10.27 13.69 10.28C13.61 10.29 12.09 11.34 9.1 13.41C8.62 13.75 8.19 13.91 7.81 13.9C7.39 13.89 6.6 13.69 6.01 13.53C5.3 13.33 4.75 13.22 4.8 12.82C4.82 12.61 5.1 12.4 5.63 12.19C8.83 10.79 10.91 9.86 11.89 9.39C14.67 7.99 15.35 7.76 15.79 7.75C15.87 7.75 16.07 7.77 16.19 7.87C16.28 7.95 16.31 8.06 16.32 8.14C16.33 8.22 16.34 8.4 16.33 8.55L16.64 8.8Z"
    />
  </svg>
);

export const Settings = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
  >
    <path
      d="M22.7922 15.1778C21.6555 14.5178 20.9589 13.3078 20.9589 12C20.9589 10.6922 21.6555 9.48221 22.7922 8.82221C22.9878 8.69999 23.0611 8.45556 22.9389 8.26L20.8978 4.74C20.8244 4.60555 20.69 4.53224 20.5556 4.53224C20.4822 4.53224 20.4089 4.55667 20.3478 4.59334C19.7855 4.91111 19.15 5.08222 18.5144 5.08222C17.8667 5.08222 17.2311 4.9111 16.6567 4.5811C15.52 3.9211 14.8233 2.72333 14.8233 1.41555C14.8233 1.18333 14.64 1 14.42 1H9.57999C9.35999 1 9.17667 1.18333 9.17667 1.41555C9.17667 2.72333 8.48 3.9211 7.34334 4.5811C6.76889 4.9111 6.13335 5.08222 5.48557 5.08222C4.85002 5.08222 4.21446 4.91111 3.65224 4.59334C3.45668 4.47111 3.21222 4.54444 3.10222 4.74L1.0489 8.26C1.01223 8.32111 1 8.39445 1 8.45556C1 8.60223 1.07335 8.73666 1.20779 8.82221C2.34446 9.48221 3.04113 10.68 3.04113 11.9878C3.04113 13.3078 2.34444 14.5178 1.21999 15.1778H1.20779C1.01224 15.3 0.938874 15.5444 1.0611 15.74L3.10222 19.26C3.17556 19.3944 3.31 19.4678 3.44444 19.4678C3.51778 19.4678 3.59113 19.4433 3.65224 19.4067C4.80113 18.7589 6.20667 18.7589 7.34334 19.4189C8.46778 20.0789 9.16444 21.2767 9.16444 22.5844C9.16444 22.8167 9.34776 23 9.57999 23H14.42C14.64 23 14.8233 22.8167 14.8233 22.5844C14.8233 21.2767 15.52 20.0789 16.6567 19.4189C17.2311 19.0889 17.8667 18.9178 18.5144 18.9178C19.15 18.9178 19.7855 19.0889 20.3478 19.4067C20.5433 19.5289 20.7878 19.4556 20.8978 19.26L22.9511 15.74C22.9878 15.6789 23 15.6055 23 15.5444C23 15.3978 22.9267 15.2633 22.7922 15.1778ZM12 15.6667C9.97111 15.6667 8.33333 14.0289 8.33333 12C8.33333 9.97111 9.97111 8.33333 12 8.33333C14.0289 8.33333 15.6667 9.97111 15.6667 12C15.6667 14.0289 14.0289 15.6667 12 15.6667Z"
      fill="currentColor"
    ></path>
  </svg>
);
export const Refresh = () => (
  <svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g id="i-refresh">
      <path
        fill="url(#grd-svg)"
        clipRule="evenodd"
        d="M4.39961 12.5C4.39961 8.30261 7.80225 4.89998 11.9996 4.89998C14.5562 4.89998 16.8192 6.16236 18.1973 8.09998H16.0004C15.5033 8.09998 15.1004 8.50292 15.1004 8.99998C15.1004 9.49703 15.5033 9.89998 16.0004 9.89998H19.7285C19.7417 9.90027 19.755 9.90027 19.7683 9.89998H20.5004C20.9974 9.89998 21.4004 9.49703 21.4004 8.99998V4.49998C21.4004 4.00292 20.9974 3.59998 20.5004 3.59998C20.0033 3.59998 19.6004 4.00292 19.6004 4.49998V6.9685C17.8918 4.62463 15.1244 3.09998 11.9996 3.09998C6.80813 3.09998 2.59961 7.3085 2.59961 12.5C2.59961 17.6915 6.80813 21.9 11.9996 21.9C16.3913 21.9 20.0775 18.8891 21.1108 14.8203C21.2332 14.3385 20.9418 13.8488 20.4601 13.7264C19.9783 13.6041 19.4886 13.8954 19.3662 14.3772C18.5307 17.6672 15.5481 20.1 11.9996 20.1C7.80225 20.1 4.39961 16.6973 4.39961 12.5Z"
      ></path>{" "}
    </g>
    <use xlinkHref="#i-refresh"></use>
  </svg>
);
export const SaverInfoIcon = () => (
  <svg viewBox="0 0 31 32" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <g id="i-saver">
      <path
        fill="url(#grd-svg)"
        clipRule="evenodd"
        d="M15.3078 1.77827C15.7064 1.39599 16.3395 1.40923 16.7217 1.80783L20.9689 6.23633C21.2428 6.47474 21.4729 6.76205 21.6459 7.08464C21.8481 7.43172 22 7.84819 22 8.32698V16.7012C22 18.2484 20.7455 19.5 19.2 19.5C17.6536 19.5 16.4 18.2464 16.4 16.7V14.3999C16.4 13.8476 15.9523 13.3999 15.4 13.3999H14V20.5C14 20.569 13.9965 20.6372 13.9897 20.7045C13.9515 21.08 13.8095 21.4249 13.5927 21.7098C13.2274 22.19 12.6499 22.5 12 22.5H4C3.30964 22.5 2.70098 22.1502 2.34157 21.6182C2.12592 21.299 2.00001 20.9142 2 20.5V5.4999C2 3.84305 3.34315 2.4999 5 2.4999H11C12.6569 2.4999 14 3.84305 14 5.4999V11.3999H15.4C17.0569 11.3999 18.4 12.7431 18.4 14.3999V16.7C18.4 17.1418 18.7582 17.5 19.2 17.5C19.6427 17.5 20 17.1422 20 16.7012V11.3292C19.6872 11.4397 19.3506 11.4999 19 11.4999C17.3431 11.4999 16 10.1568 16 8.4999C16 7.28851 16.718 6.24482 17.7517 5.77117L15.2783 3.19217C14.896 2.79357 14.9092 2.16055 15.3078 1.77827ZM19.6098 7.70731C19.441 7.57725 19.2296 7.4999 19 7.4999C18.4477 7.4999 18 7.94762 18 8.4999C18 9.05219 18.4477 9.4999 19 9.4999C19.5523 9.4999 20 9.05219 20 8.4999C20 8.34084 19.9629 8.19045 19.8968 8.05693C19.8303 7.95164 19.7349 7.83559 19.6098 7.70731ZM5.21572 4.72463C4.66343 4.72463 4.21572 5.17235 4.21572 5.72463V9.72463C4.21572 10.2769 4.66343 10.7246 5.21572 10.7246H10.7157C11.268 10.7246 11.7157 10.2769 11.7157 9.72463V5.72463C11.7157 5.17235 11.268 4.72463 10.7157 4.72463H5.21572Z"
      ></path>{" "}
    </g>
    <use xlinkHref="#i-saver"></use>
  </svg>
);

export const SlippageIcon = () => (
  <svg viewBox="0 0 31 30" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <defs>
      <g id="i-SlippageIcon">
        <path
          fill="url(#grd-svg)"
          d="M21.95 6.8C21.2 6.42 20.37 6 19 6C17.63 6 16.8 6.42 16.05 6.8C15.4 7.12 14.87 7.4 14 7.4C13.1 7.4 12.6 7.15 11.95 6.8C11.2 6.43 10.38 6 9 6C7.62 6 6.8 6.42 6.05 6.8C5.67263 6.99159 5.33909 7.16295 4.95347 7.27299C4.44098 7.41925 4 7.83204 4 8.365C4 8.89795 4.43551 9.34084 4.96084 9.25103C5.78633 9.10991 6.39549 8.80356 6.95 8.53C7.6 8.2 8.12 7.93 9 7.93C9.88 7.93 10.4 8.18 11.05 8.53C11.8 8.91 12.62 9.33 14 9.33C15.38 9.33 16.2 8.9 16.95 8.53C17.6 8.21 18.13 7.93 19 7.93C19.9 7.93 20.4 8.18 21.05 8.53C21.6041 8.81076 22.202 9.11336 23.029 9.25226C23.56 9.34145 24 8.89348 24 8.355C24 7.81652 23.5548 7.40161 23.0353 7.25985C22.6526 7.15542 22.3265 6.99113 21.95 6.8Z"
        ></path>
        <path
          fill="url(#grd-svg)"
          d="M16.05 12.8C16.8 12.43 17.65 12 19 12C20.35 12 21.2 12.42 21.95 12.8C22.3266 13.0028 22.6528 13.172 23.0356 13.2784C23.5544 13.4227 24 13.8365 24 14.375C24 14.9135 23.56 15.3615 23.029 15.2723C22.202 15.1334 21.6041 14.8308 21.05 14.55C20.4 14.2 19.9 13.95 19 13.95C18.13 13.95 17.6 14.23 16.95 14.55C16.2 14.92 15.38 15.35 14 15.35C12.6288 15.35 11.8105 14.9354 11.0644 14.5573L11.05 14.55C10.4 14.2 9.87 13.95 9 13.95C8.13 13.95 7.6 14.23 6.95 14.55C6.39771 14.8225 5.79119 15.1275 4.97072 15.2693C4.44011 15.3611 4 14.9135 4 14.375C4 13.8365 4.44562 13.4227 4.9644 13.2784C5.34722 13.172 5.67344 13.0028 6.05 12.8C6.8 12.43 7.62 12 9 12C10.3712 12 11.1895 12.4146 11.9356 12.7927L11.95 12.8C12.6 13.15 13.1 13.4 14 13.4C14.9 13.4 15.4 13.15 16.05 12.8Z"
        ></path>
        <path
          d="M16.05 18.8C16.8 18.43 17.65 18 19 18C20.35 18 21.2 18.42 21.95 18.8C22.3266 19.0028 22.6528 19.172 23.0356 19.2784C23.5544 19.4227 24 19.8365 24 20.375C24 20.9135 23.56 21.3615 23.029 21.2723C22.202 21.1334 21.6041 20.8308 21.05 20.55C20.4 20.2 19.9 19.95 19 19.95C18.13 19.95 17.6 20.23 16.95 20.55C16.2 20.92 15.38 21.35 14 21.35C12.6288 21.35 11.8105 20.9354 11.0644 20.5573L11.05 20.55C10.4 20.2 9.87 19.95 9 19.95C8.13 19.95 7.6 20.23 6.95 20.55C6.39771 20.8225 5.79119 21.1275 4.97072 21.2693C4.44011 21.3611 4 20.9135 4 20.375C4 19.8365 4.44562 19.4227 4.9644 19.2784C5.34722 19.172 5.67344 19.0028 6.05 18.8C6.8 18.43 7.62 18 9 18C10.3712 18 11.1895 18.4146 11.9356 18.7927L11.95 18.8C12.6 19.15 13.1 19.4 14 19.4C14.9 19.4 15.4 19.15 16.05 18.8Z"
          fill="url(#grd-svg)"
        />
      </g>
    </defs>
    <use xlinkHref="#i-SlippageIcon"></use>
  </svg>
);

export const Twitter = () => (
  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <path
      fill="url(#grd-svg)"
      d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM17.15 9.36C17.15 9.37 17.15 9.38 17.15 9.39C17.15 13.05 14.42 17.27 9.28 17.27C7.73 17.27 6.29 16.83 5.08 16.08C5.3 16.11 5.53 16.12 5.76 16.12C7.06 16.12 8.25 15.69 9.2 14.96C7.98 14.94 6.93 14.12 6.58 12.98C6.75 13.01 6.93 13.03 7.11 13.03C7.37 13.03 7.62 12.99 7.85 12.92C6.57 12.65 5.6 11.52 5.6 10.16V10.13C5.97 10.33 6.4 10.45 6.85 10.46C6.09 9.95 5.59 9.11 5.59 8.16C5.59 7.63 5.73 7.14 5.98 6.72C7.37 8.42 9.43 9.55 11.76 9.67C11.71 9.47 11.69 9.26 11.69 9.04C11.69 7.5 12.95 6.24 14.49 6.24C15.29 6.24 16.01 6.57 16.52 7.1C17.16 6.98 17.76 6.75 18.3 6.44C18.1 7.08 17.66 7.63 17.09 7.98C17.65 7.92 18.18 7.77 18.68 7.56C18.3 8.12 17.82 8.61 17.26 9.01C17.26 9.13 17.26 9.24 17.26 9.36H17.15Z"
    />
  </svg>
);
export const DownArrow = () => {
  const { isDarkMode } = useTheme();

  if (isDarkMode) {
    // Dark mode version with gradient
    return (
      <svg
        width={16}
        height={10}
        viewBox="0 0 12 9"
        xmlns="http://www.w3.org/2000/svg"
        style={{ transform: "translateY(0px)" }}
      >
        <defs>
          <linearGradient id="grd-svg">
            <stop className="stop1" offset="0%"></stop>
            <stop className="stop2" offset="100%"></stop>
          </linearGradient>
        </defs>
        <defs>
          <g id="i-da">
            <path fill="url(#grd-svg)" d="M0.97168 1L6.20532 6L11.439 1"></path>
          </g>
        </defs>
        <use xlinkHref="#i-da"></use>
      </svg>
    );
  } else {
    // Light mode version with black stroke
    return (
      <svg
        width="10"
        height="6"
        viewBox="0 0 10 6"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1 1L5 5L9 1"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    );
  }
};

export const Globe = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    width="24"
    height="24"
  >
    <defs>
      <linearGradient id="grd-svg">
        <stop className="stop1" offset="0%"></stop>
        <stop className="stop2" offset="100%"></stop>
      </linearGradient>
    </defs>
    <path
      fill="url(#grd-svg)"
      d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM4 12C4 11.39 4.08 10.79 4.21 10.22L8.99 15V16C8.99 17.1 9.89 18 10.99 18V19.93C7.06 19.43 4 16.07 4 12ZM17.89 17.4C17.63 16.59 16.89 16 16 16H15V13C15 12.45 14.55 12 14 12H8V10H10C10.55 10 11 9.55 11 9V7H13C14.1 7 15 6.1 15 5V4.59C17.93 5.78 20 8.65 20 12C20 14.08 19.2 15.97 17.89 17.4Z"
    />
  </svg>
);
