import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";

const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }

    // We can use authenticatedAddress if needed
    // const authenticatedAddress = authResult.address;
    const body = await request.json();
    const { chain_id } = body;

    if (!chain_id) {
      return NextResponse.json(
        { error: "Chain ID is required" },
        { status: 400 }
      );
    }

    const rpcUrl = RPC_URLS[chain_id];
    if (!rpcUrl) {
      return NextResponse.json(
        { error: "Unsupported chain ID" },
        { status: 400 }
      );
    }

    const provider = new ethers.JsonRpcProvider(rpcUrl);

    const blockNumber = await provider.getBlockNumber();

    return NextResponse.json({
      blockNumber: blockNumber.toString(),
    });
  } catch (error) {
    console.warn("RPC error:", error);
    return NextResponse.json(
      { error: "Failed to get block number" },
      { status: 500 }
    );
  }
}
