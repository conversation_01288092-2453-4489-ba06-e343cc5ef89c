async function fetchBlockNumber(chain_id, authToken) {
  try {
    if (!chain_id) {
      console.warn("Invalid or missing parameters for block number");
      return null;
    }
    const response = await fetch("/api/rpc-call/get-block-number", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bear<PERSON> ${authToken}`,
      },
      body: JSON.stringify({ chain_id }),
    });

    const data = await response.json();

    if (response.ok) {
      console.log("Block Number:", data.blockNumber);
      return data.blockNumber;
    } else {
      console.warn("Error:", data.error);
    }
  } catch (error) {
    console.warn("Failed to fetch block number:", error);
  }
}
export default fetchBlockNumber;
