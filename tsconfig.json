{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "noImplicitAny": false, "noImplicitThis": false, "alwaysStrict": false, "strictBindCallApply": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictPropertyInitialization": false, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "components/Swap.jsx", "components/lib/constants.js", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}