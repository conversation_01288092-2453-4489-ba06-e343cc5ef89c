{"app_id": "2105", "connection_id": "9c099a20-87b3-40f5-bed8-0b7a56811975", "session_id": "01e44cad-9c32-44ea-89b0-13613b939ecc", "account_id": "6RduSLUXdUbR4GQxM1Q9qQGz44vGMwXhdT9we9SkEGgsp8LS", "user_pub_key": "0x5e06a82903f34de2513a9b0f44d1fb6f51cdfe68b27c0f4ad79e87f622c8e871", "app_pub_key": "6RduSLUXdUbR4GQxM1Q9qQGz44vGMwXhdT9we9SkEGgsp8LS", "data_service_pub_key": "0x876819ba5649913eea9e3082331313c6aa136e6485b3a9078469d7996f9695e9", "id": "e46cba55-950d-48f7-84df-726317c91133", "event_type": "DEX_SWAP", "timestamp": "2025-05-21T16:30:16.665Z", "payload": {"cere_wallet_id": "6RduSLUXdUbR4GQxM1Q9qQGz44vGMwXhdT9we9SkEGgsp8LS", "chain_id": "8453", "timestamp": "2025-05-21T16:30:16.665Z", "reference_id": "**********", "tx_hash": "0x1acbd2f920ee30a9e7be122a32e219118ee29a3e897a02ddfbee4b9300b9a0d1", "user_dapp_connected_wallet": "******************************************", "token_pair": "BRETT-ETH", "sell_token_address": "******************************************", "sell_token_amount": "0.063567", "buy_token_address": "******************************************", "buy_token_amount": "0.********"}, "signing": "0x000000", "signature": "0xcad0a2bb03370e3fe313af4f3ea992f51b4180dab88a6504f797d8a7742083e47900d00ad1dc291746f33f3c24ed52a056f24c148a18d00b61bd7a2ecfcf4c0f"}