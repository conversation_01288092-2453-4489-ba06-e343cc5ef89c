/* Content Transition Styles */
.about-content,
.stake-content,
.swap-container {
  transition: opacity 0.3s ease;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Ensure the whole container maintains its height during transitions */
.whole-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Make sure the footer stays at the bottom */
.footer-container {
  margin-top: auto;
}
