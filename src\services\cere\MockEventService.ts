"use client";

/**
 * Mock implementation of EventService that does nothing
 * Used when Cere tracking is disabled to prevent network requests
 */
class MockEventService {
  private static instance: MockEventService | null = null;

  constructor(private token: string) {
    if (MockEventService.instance) {
      return MockEventService.instance;
    }

    console.log("Created MockEventService (no-op implementation)");
    MockEventService.instance = this;
  }

  /**
   * Mock initialization that does nothing
   */
  async init() {
    console.log("MockEventService.init() called - no operation performed");
    return Promise.resolve();
  }

  /**
   * Mock wallet connect event that does nothing
   */
  async dispatchWalletConnectEvent() {
    console.log(
      "MockEventService.dispatchWalletConnectEvent() called - no operation performed"
    );
    return Promise.resolve(true);
  }

  /**
   * Mock swap event that does nothing
   */
  async dispatchSwapEvent(swapDetails: any) {
    console.log(
      "MockEventService.dispatchSwapEvent() called - no operation performed"
    );
    return Promise.resolve(true);
  }
}

export default MockEventService;
