import { NextResponse } from "next/server";

/**
 * Middleware function to handle CORS for wallet connections
 * This is especially important for mobile devices and in-app browsers
 */
export function middleware(request) {
  // Define CORS headers to allow all origins
  const corsHeaders = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers":
      "Content-Type, Authorization, X-Requested-With",
    "Access-Control-Allow-Credentials": "true",
  };

  // Handle preflight OPTIONS requests
  if (request.method === "OPTIONS") {
    return new NextResponse(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  // For regular requests, add CORS headers to the response
  const response = NextResponse.next();

  // Apply all CORS headers
  Object.entries(corsHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}

// Apply this middleware only to API routes
export const config = {
  matcher: [
    // Match all API routes
    "/api/:path*",
  ],
};
