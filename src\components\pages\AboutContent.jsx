"use client";

import { useEffect, useState } from "react";
import useIsMobile from "../../hooks/useIsMobile";
import { motion } from "framer-motion";

export default function AboutContent() {
  const isMobile = useIsMobile();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      className={`about-container ${isMobile ? "mobile-about" : ""}`}
      initial="hidden"
      animate={isVisible ? "visible" : "hidden"}
      variants={containerVariants}
    >
      <motion.div className="about-section" variants={itemVariants}>
        <h2>Beta Testing Phase</h2>
        <p>
          We&apos;re currently in a controlled beta testing phase, focusing on:
        </p>
        <ul>
          <li>Gathering user feedback on core functionality</li>
          <li>Optimizing performance across different devices and networks</li>
          <li>Fine-tuning our Cere Network integration</li>
          <li>Implementing iterative improvements based on real-world usage</li>
        </ul>
        <p>
          This feedback and adjustment period is crucial before our full public
          release. We&apos;re committed to delivering a polished, reliable
          platform that meets the needs of our community.
        </p>
      </motion.div>
      <motion.div className="about-section" variants={itemVariants}>
        <h2>Cere Network Integration</h2>
        <p>
          We&apos;ve partnered with Cere Network for our beta release to test
          cutting-edge decentralized data storage solutions. This integration
          allows us to:
        </p>
        <ul>
          <li>Securely store user preferences and settings</li>
          <li>Track anonymous usage patterns to improve the platform</li>
          <li>
            Implement decentralized analytics without compromising privacy
          </li>
          <li>Create a foundation for future decentralized features</li>
        </ul>
        <p>
          This partnership represents our commitment to building on truly
          decentralized infrastructure, moving beyond the limitations of
          traditional centralized storage solutions.
        </p>
      </motion.div>

      <motion.div className="about-section" variants={itemVariants}>
        <h2>Get Involved</h2>
        <p>
          We value your input! As a beta user, your feedback helps shape the
          future of Pineapple. Here&apos;s how you can contribute:
        </p>
        <ul>
          <li>Report bugs or issues you encounter</li>
          <li>Suggest features or improvements</li>
          <li>Share your experience with the platform</li>
          <li>Join our community channels</li>
        </ul>
        <p>
          Together, we&apos;re building the next generation of DeFi tools that
          prioritize user experience without compromising on functionality or
          security.
        </p>
      </motion.div>
    </motion.div>
  );
}
