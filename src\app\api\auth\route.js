import { NextResponse } from "next/server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error("JWT_SECRET is not defined in environment variables");
}

export async function POST(request) {
  try {
    if (!JWT_SECRET) {
      console.error("JWT_SECRET is not defined, authentication is disabled");
      return NextResponse.json(
        { error: "Authentication is disabled (JWT_SECRET not configured)" },
        { status: 500 }
      );
    }

    const body = await request.json();
    const { walletAddress } = body;

    if (!walletAddress) {
      return NextResponse.json(
        { error: "Wallet address is required" },
        { status: 400 }
      );
    }

    const token = jwt.sign(
      {
        address: walletAddress,
        exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24,
      },
      JWT_SECRET
    );

    console.log(`Generated token for wallet: ${walletAddress}`);
    return NextResponse.json({ token });
  } catch (error) {
    console.warn("Auth error:", error);
    return NextResponse.json(
      { error: "Authentication failed" },
      { status: 500 }
    );
  }
}
