'use client';

import React, { useState, useEffect } from "react";

function Iframe({ buyToken, chain_id, subTab }) {
  function Dexscreener() {
    const [currentTab, setCurrentTab] = useState("Chart");

    // Determine the iframe source based on currentTab
    let iframeSrc;
    const contractAddress = buyToken;
    let chainName = "ethereum";
    if (chain_id === 8453) chainName = "base";
    else if (chain_id === 56) chainName = "bsc";

    if (currentTab === "Chart") {
      iframeSrc = `https://dexscreener.com/${chainName}/${contractAddress}?embed=1&theme=dark&trades=0&info=0`;
    } else if (currentTab === "chartorderbook") {
      iframeSrc = `https://dexscreener.com/${chainName}/${contractAddress}?embed=1&theme=dark&trades=1&info=0&chart=0`;
    }

    // Calculate glider position for 2 tabs
    // Adjust the numeric values if your layout differs
    const gliderLeft = currentTab === "Chart" ? "0px" : "calc(50%)";
    const gliderLeftBottomRadius = currentTab === "Chart" ? "15px" : "0px";
    const gliderRightBottomRadius = currentTab === "Chart" ? "0px" : "15px";
    // Explanation:
    //  - "120px" matches .tab-button width
    //  - "2rem" or some gap to account for spacing between tabs
    // Tweak as needed for your design

    return (
      <div className="iframe-container">
        <div className="tab-selector">
          {/* Glider behind the tabs */}
          <div
            className="glider-chart"
            style={{
              left: gliderLeft,
              borderBottomLeftRadius: gliderLeftBottomRadius,
              borderBottomRightRadius: gliderRightBottomRadius,
            }}
          ></div>

          <div
            className={currentTab === "Chart" ? "tab-button tba" : "tab-button"}
            onClick={() => setCurrentTab("Chart")}
          >
            Chart
          </div>

          <div
            className={
              currentTab === "chartorderbook" ? "tab-button tba" : "tab-button"
            }
            onClick={() => setCurrentTab("chartorderbook")}
          >
            Trades
          </div>
        </div>

        <iframe src={iframeSrc} title={currentTab} className="iframe" />
      </div>
    );
  }

  if (subTab === "Dexscreener") {
    return <Dexscreener />;
  } else {
    return null;
  }
}

export default Iframe;
