"use client";

import { createAppKit } from "@reown/appkit/react";
import { EthersAdapter } from "@reown/appkit-adapter-ethers";
import { mainnet, base } from "@reown/appkit/networks";
import React from "react";

declare global {
  interface Window {
    reown?: {
      appkit?: {
        modal?: {
          open: () => void;
        };
        openModal?: () => void;
      };
    };
  }
}

const getMetadata = () => {
  const url =
    typeof window !== "undefined"
      ? process.env.NODE_ENV === "development"
        ? window.location.origin
        : "https://pineappledex.com"
      : "https://pineappledex.com";

  return {
    name: "Pineapple DEX",
    description: "The Freshest way to trade",
    url: url,
    icons: [
      "https://www.dextools.io/resources/tokens/logos/ether/******************************************.jpg",
    ],
  };
};

const projectId = "0205249ed8e699b2d8664689a5466cbf";

if (typeof window !== "undefined") {
  try {
    createAppKit({
      projectId,
      metadata: getMetadata(),
      adapters: [new EthersAdapter()],
      networks: [mainnet, base],

      features: {
        swaps: false,
        send: false,
        onramp: false,
        socials: [],
        email: false,
        analytics: false,
      },

      enableWallets: true,
      enableInjected: true,
      enableCoinbase: true,
      enableWalletConnect: true,

      showWallets: true,

      debug: false,
    });

    console.log("AppKit initialized successfully");

    const handleOpenWalletModal = () => {
      console.log("Received open-wallet-modal event");
      if (window.reown && window.reown.appkit && window.reown.appkit.modal) {
        console.log("Opening wallet modal via event listener");
        window.reown.appkit.modal.open();
      }
    };

    window.addEventListener("open-wallet-modal", handleOpenWalletModal);

    window.addEventListener("error", (event) => {
      if (
        event.message?.includes("AppKit") ||
        event.message?.includes("Cross-Origin") ||
        event.message?.includes("COOP")
      ) {
        console.warn("Caught AppKit error:", event.message);

        event.preventDefault();
        return true;
      }
      return false;
    });
  } catch (error) {
    console.warn("Error initializing AppKit:", error);
  }
}

export function AppKit({ children }: { children: React.ReactNode }) {
  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.warn("Error in AppKit component:", error);

      error.preventDefault();
      return true;
    };

    window.addEventListener("error", handleError);

    return () => {
      window.removeEventListener("error", handleError);
    };
  }, []);

  return <>{children}</>;
}
