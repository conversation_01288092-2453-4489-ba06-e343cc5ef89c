/* Stake Page Styles */
.stake-container {
  margin-top: 100px;
  margin-bottom: 150px;
  max-width: 900px;
  padding: 30px;
  color: var(--text);
  background: var(--box-bg);
  border-radius: 25px;
  box-shadow: rgba(0, 0, 0, 0.24) 12px 16px 24px,
    rgba(0, 0, 0, 0.24) 12px 8px 12px, rgba(0, 0, 0, 0.32) 4px 4px 8px;
  transition: all 0.3s ease-in-out;
  text-align: center;
}

.stake-section {
  margin-bottom: 40px;
  padding: 25px;
  background: var(--box-inner);
  border-radius: 15px;
  border: solid 1px var(--border);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stake-section:hover {
  transform: translateY(-5px);
  background: var(--box-hover);
  border: solid 1px var(--primary-color);
}

.stake-section h2 {
  font-family: "Monument", sans-serif;
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--primary-color);
  font-weight: 500;
}

.stake-section p {
  font-family: "Poppins", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 15px;
  color: var(--text);
}

.stake-section ul {
  margin-left: 20px;
  margin-bottom: 15px;
  text-align: left;
}

.stake-section li {
  font-family: "Poppins", sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 8px;
  list-style-type: disc;
  color: var(--text);
}

.coming-soon-badge {
  display: inline-block;
  background: var(--primary-color);
  color: #000;
  font-family: "Monument", sans-serif;
  font-weight: 600;
  padding: 8px 20px;
  border-radius: 20px;
  margin-bottom: 30px;
  letter-spacing: 1px;
  animation: pulse 2s infinite;
}

.audit-status {
  display: inline-block;
  background: rgba(255, 165, 0, 0.2);
  color: var(--primary-color);
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  padding: 8px 20px;
  border-radius: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
  border: 1px solid var(--primary-color);
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: var(--primary-color);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
  }
}

/* Mobile-specific styles */

.mobile-stake 

.mobile-stake 

.mobile-stake .stake-section {
  padding: 15px !important;
  margin-bottom: 20px !important;
}

.mobile-stake .stake-section:hover {
  transform: none !important; /* Disable hover effect on mobile */
}

.mobile-stake .stake-section h2 {
  font-size: 1.4rem !important;
  margin-bottom: 15px !important;
}

.mobile-stake .stake-section p,
.mobile-stake .stake-section li {
  font-size: 0.95rem !important;
  line-height: 1.5 !important;
  margin-bottom: 10px !important;
}

.mobile-stake .stake-section ul {
  margin-left: 15px !important;
  margin-bottom: 15px !important;
}

.mobile-stake 

.mobile-stake 

/* Media queries */
@media (max-width: 768px) {
  .stake-container {
    margin: 80px auto 50px;
    padding: 15px;
    width: 95%;
    box-shadow: none;
  }

  .stake-section {
    padding: 15px;
    margin-bottom: 20px;
  }

  .stake-section h2 {
    font-size: 1.4rem;
  }

}
