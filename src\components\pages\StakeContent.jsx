"use client";

import { useEffect, useState } from "react";
import useIsMobile from "../../hooks/useIsMobile";
import { motion } from "framer-motion";

export default function StakeContent() {
  const isMobile = useIsMobile();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.div
      className={`stake-container ${isMobile ? "mobile-stake" : ""}`}
      initial="hidden"
      animate={isVisible ? "visible" : "hidden"}
      variants={containerVariants}
    >
      <motion.div variants={itemVariants}>
        <div className="coming-soon-badge">COMING SOON</div>
      </motion.div>

      <motion.div className="stake-section" variants={itemVariants}>
        <h2>Audit Status</h2>
        <div className="audit-status">AUDIT IN PROGRESS</div>
        <p>
          We take security seriously. Our staking contracts are currently
          undergoing a comprehensive audit by a leading blockchain security firm
          to ensure the safety of your funds.
        </p>
        <ul>
          <li>Smart contract audit in progress</li>
          <li>Security testing and vulnerability assessment</li>
          <li>Implementation of best practices and security standards</li>
          <li>Multiple rounds of review and testing</li>
        </ul>
        <p>
          The staking platform will be launched once the audit is complete and
          all security recommendations have been implemented.
        </p>
      </motion.div>

      <motion.div className="stake-section" variants={itemVariants}>
        <h2>Stay Updated</h2>
        <p>
          Join our community channels to receive updates on the staking platform
          launch, including audit completion, feature announcements, and early
          access opportunities.
        </p>
        <p>
          We&apos;re committed to building a secure, user-friendly staking
          experience that rewards our community members for their support and
          participation.
        </p>
      </motion.div>
    </motion.div>
  );
}
