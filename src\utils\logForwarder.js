/**
 * Log Forwarder Utility
 *
 * This utility intercepts console logs and forwards them to the server.
 * It's designed to work in browser environments and gracefully handles
 * serialization of complex objects.
 */

const safeStringify = (obj) => {
  try {
    const seen = new WeakSet();
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === "object" && value !== null) {
        if (seen.has(value)) {
          return "[Circular Reference]";
        }
        seen.add(value);
      }

      if (value instanceof Error) {
        return {
          name: value.name,
          message: value.message,
          stack: value.stack,
          __type: "Error",
        };
      }
      if (value instanceof Date) {
        return { value: value.toISOString(), __type: "Date" };
      }
      if (value instanceof RegExp) {
        return { value: value.toString(), __type: "RegExp" };
      }
      if (typeof value === "function") {
        return { name: value.name || "anonymous", __type: "Function" };
      }
      if (value === undefined) {
        return { __type: "undefined" };
      }
      return value;
    });
  } catch (error) {
    return `[Unserializable Object: ${error.message}]`;
  }
};

const processArgs = (args) => {
  return Array.from(args).map((arg) => {
    if (arg === undefined) return "undefined";
    if (arg === null) return "null";

    if (typeof arg === "object") {
      try {
        return safeStringify(arg);
      } catch (e) {
        return `[Unserializable Object]`;
      }
    }

    return String(arg);
  });
};

const getDeviceInfo = () => {
  if (typeof window === "undefined") return "Server";

  const userAgent = navigator.userAgent;
  let deviceInfo = "Unknown";

  if (/iPhone|iPad|iPod/i.test(userAgent)) {
    deviceInfo = "iOS";
  } else if (/Android/i.test(userAgent)) {
    deviceInfo = "Android";
  } else if (/Windows Phone/i.test(userAgent)) {
    deviceInfo = "Windows Phone";
  } else if (/Windows/i.test(userAgent)) {
    deviceInfo = "Windows";
  } else if (/Macintosh/i.test(userAgent)) {
    deviceInfo = "Mac";
  } else if (/Linux/i.test(userAgent)) {
    deviceInfo = "Linux";
  }

  if (/Chrome/i.test(userAgent) && !/Edge/i.test(userAgent)) {
    deviceInfo += " Chrome";
  } else if (/Firefox/i.test(userAgent)) {
    deviceInfo += " Firefox";
  } else if (/Safari/i.test(userAgent) && !/Chrome/i.test(userAgent)) {
    deviceInfo += " Safari";
  } else if (/Edge/i.test(userAgent)) {
    deviceInfo += " Edge";
  }

  return deviceInfo;
};

export const initLogForwarding = () => {
  if (typeof window === "undefined") return;

  if (process.env.NODE_ENV === "production") {
    console.log("[Log Forwarder] Disabled in production mode");
    return null;
  }

  const deviceInfo = getDeviceInfo();

  const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
  };

  const sendLogToServer = (type, args) => {
    try {
      if (args[0] === "[Log Forwarder]") return;

      const messages = processArgs(args);

      fetch("/api/log-forwarder", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          type,
          messages,
          deviceInfo,
          timestamp: new Date().toISOString(),
        }),

        keepalive: true,
      }).catch((e) => {
        originalConsole.error("[Log Forwarder] Failed to send log:", e);
      });
    } catch (e) {
      originalConsole.error("[Log Forwarder] Error processing log:", e);
    }
  };

  console.log = (...args) => {
    originalConsole.log(...args);
    sendLogToServer("log", args);
  };

  console.error = (...args) => {
    originalConsole.error(...args);
    sendLogToServer("error", args);
  };

  console.warn = (...args) => {
    originalConsole.warn(...args);
    sendLogToServer("warn", args);
  };

  console.info = (...args) => {
    originalConsole.info(...args);
    sendLogToServer("info", args);
  };

  originalConsole.log("[Log Forwarder] Initialized for device:", deviceInfo);

  window.addEventListener("error", (event) => {
    sendLogToServer("error", [
      `Unhandled Error: ${event.message}`,
      `at ${event.filename}:${event.lineno}:${event.colno}`,
      event.error,
    ]);
  });

  window.addEventListener("unhandledrejection", (event) => {
    sendLogToServer("error", ["Unhandled Promise Rejection:", event.reason]);
  });

  return {
    disable: () => {
      console.log = originalConsole.log;
      console.error = originalConsole.error;
      console.warn = originalConsole.warn;
      console.info = originalConsole.info;
      originalConsole.log("[Log Forwarder] Disabled");
    },
  };
};
