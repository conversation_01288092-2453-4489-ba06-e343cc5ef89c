"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

const TransactionEffect = () => {
  const [transactionState, setTransactionState] = useState(null);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const handleTransactionStateChange = (event) => {
      const { state, timestamp } = event.detail;
      console.log(
        `Transaction state changed to: ${state} at ${new Date(
          timestamp
        ).toLocaleTimeString()}`
      );

      setTransactionState(state);
      setVisible(true);

      if (state === "success") {
        setTimeout(() => {
          setVisible(false);
        }, 1500);
      }
    };

    window.addEventListener(
      "transactionStateChange",
      handleTransactionStateChange
    );

    return () => {
      window.removeEventListener(
        "transactionStateChange",
        handleTransactionStateChange
      );
    };
  }, []);

  const particleCount = 16;
  const particles = Array.from({ length: particleCount }).map((_, i) => {
    const angle = (i / particleCount) * 360;
    const distance = Math.random() * 20 + 30;
    const x = Math.cos(angle * (Math.PI / 180)) * distance;
    const y = Math.sin(angle * (Math.PI / 180)) * distance;
    const size = Math.random() * 4 + 2;

    return {
      hidden: { x: 0, y: 0, opacity: 0, width: size, height: size },
      visible: {
        x,
        y,
        opacity: [0, 1, 0],
        scale: [1, 1.5, 0.5],
        transition: {
          duration: Math.random() * 0.3 + 0.5,
          times: [0, 0.2, 1],
          delay: i * 0.01,
        },
      },
    };
  });

  return (
    <AnimatePresence>
      {visible && transactionState === "success" && (
        <motion.div
          className="tab-effect-container"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0, transition: { duration: 0.3 } }}
        >
          {/* Particles */}
          {particles.map((particle, index) => (
            <motion.div
              key={index}
              className="tab-particle"
              variants={particle}
              initial="hidden"
              animate="visible"
              style={{
                width: `${particle.hidden.width}px`,
                height: `${particle.hidden.height}px`,
              }}
            />
          ))}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TransactionEffect;
