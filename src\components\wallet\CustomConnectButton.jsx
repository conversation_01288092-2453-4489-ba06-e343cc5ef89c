"use client";

import React, { useState, useEffect, useRef } from "react";
import { useAppKitAccount, useDisconnect } from "@reown/appkit/react";
import { ModalController } from "@reown/appkit-controllers";
import useIsMobile from "../../hooks/useIsMobile";

const CustomConnectButton = () => {
  const [isHovered, setIsHovered] = useState(false);
  const [isDisconnecting, setIsDisconnecting] = useState(false);
  const dropdownRef = useRef(null);
  const hoverTimeoutRef = useRef(null);
  const { isConnected, address } = useAppKitAccount();
  const { disconnect } = useDisconnect();
  const isMobile = useIsMobile();

  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  const handleClick = () => {
    if (!isConnected) {
      try {
        console.log("Opening wallet connection modal");

        if (ModalController && typeof ModalController.open === "function") {
          console.log("Using ModalController.open()");
          ModalController.open();
        } else {
          console.log("ModalController.open not available, trying fallback");

          if (
            typeof window !== "undefined" &&
            window.reown &&
            window.reown.appkit
          ) {
            console.log("Using window.reown.appkit");
            const appKit = window.reown.appkit;

            if (appKit.modal && typeof appKit.modal.open === "function") {
              console.log("Using appKit.modal.open()");
              appKit.modal.open();
            } else if (typeof appKit.openModal === "function") {
              console.log("Using appKit.openModal()");
              appKit.openModal();
            } else {
              console.log("No direct method found, dispatching event");
              const event = new CustomEvent("open-wallet-modal");
              window.dispatchEvent(event);
            }
          } else {
            console.log("AppKit not found, dispatching event");
            const event = new CustomEvent("open-wallet-modal");
            window.dispatchEvent(event);
          }
        }
      } catch (error) {
        console.warn("Error opening wallet modal:", error);

        try {
          const event = new CustomEvent("open-wallet-modal");
          window.dispatchEvent(event);
        } catch (e) {
          console.warn("Error dispatching event:", e);
        }
      }
    }
  };

  const handleDisconnect = async (e) => {
    e.stopPropagation();

    setIsDisconnecting(true);

    try {
      if (disconnect) {
        await disconnect();
        console.log("Wallet disconnected successfully");
      } else {
        console.warn("Disconnect function not available");

        if (ModalController && typeof ModalController.open === "function") {
          ModalController.open();
        } else {
          if (
            typeof window !== "undefined" &&
            window.reown &&
            window.reown.appkit
          ) {
            const appKit = window.reown.appkit;
            if (appKit.modal && typeof appKit.modal.open === "function") {
              appKit.modal.open();
            } else if (typeof appKit.openModal === "function") {
              appKit.openModal();
            } else {
              const event = new CustomEvent("open-wallet-modal");
              window.dispatchEvent(event);
            }
          } else {
            const event = new CustomEvent("open-wallet-modal");
            window.dispatchEvent(event);
          }
        }
      }
    } catch (error) {
      console.warn("Error disconnecting wallet:", error);

      if (ModalController && typeof ModalController.open === "function") {
        ModalController.open();
      } else {
        if (
          typeof window !== "undefined" &&
          window.reown &&
          window.reown.appkit
        ) {
          const appKit = window.reown.appkit;
          if (appKit.modal && typeof appKit.modal.open === "function") {
            appKit.modal.open();
          } else if (typeof appKit.openModal === "function") {
            appKit.openModal();
          } else {
            const event = new CustomEvent("open-wallet-modal");
            window.dispatchEvent(event);
          }
        } else {
          const event = new CustomEvent("open-wallet-modal");
          window.dispatchEvent(event);
        }
      }
    } finally {
      setIsDisconnecting(false);
    }
  };

  const formatAddress = (address) => {
    if (!address) return "";
    return `${address.substring(0, 6)}...${address.substring(
      address.length - 4
    )}`;
  };

  if (isConnected && address) {
    return (
      <div
        className="wallet-dropdown-container"
        ref={dropdownRef}
        onMouseEnter={() => {
          if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
            hoverTimeoutRef.current = null;
          }
          setIsHovered(true);
        }}
        onMouseLeave={() => {
          hoverTimeoutRef.current = setTimeout(() => {
            setIsHovered(false);
          }, 300);
        }}
      >
        <div
          className={`wallet-button wallet-button-connected ${
            isMobile ? "wallet-button-mobile" : ""
          }`}
          onClick={handleClick}
        >
          <span>{formatAddress(address)}</span>
        </div>

        {isHovered && (
          <>
            {isMobile && (
              <div
                className="mobile-dropdown-backdrop"
                onClick={() => setIsHovered(false)}
              />
            )}
            <div
              className="wallet-dropdown-content"
              onMouseEnter={() => {
                if (hoverTimeoutRef.current) {
                  clearTimeout(hoverTimeoutRef.current);
                  hoverTimeoutRef.current = null;
                }
                setIsHovered(true);
              }}
              onMouseLeave={() => {
                hoverTimeoutRef.current = setTimeout(() => {
                  setIsHovered(false);
                }, 300);
              }}
            >
              <div
                className={`wallet-dropdown-item disconnect ${
                  isDisconnecting ? "disconnecting" : ""
                }`}
                onClick={handleDisconnect}
              >
                {isDisconnecting ? "Disconnecting..." : "Disconnect"}
              </div>
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div
      className={`wallet-button wallet-button-default ${
        isMobile ? "wallet-button-mobile" : ""
      }`}
      onClick={handleClick}
    >
      {isMobile ? "Connect" : "Connect Wallet"}
    </div>
  );
};

export default CustomConnectButton;
