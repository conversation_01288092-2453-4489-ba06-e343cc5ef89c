// Clean TransactionModal component
import React from "react";
import "../../styles/transaction-review.css";

const TransactionModal = ({
  isOpen,
  onClose,
  fromToken,
  toToken,
  fromAmount,
  toAmount,
  fromUsdValue,
  toUsdValue,
  isApprovalNeeded,
  currentStep,
  getStepState,
  onExecuteTransaction,
  getButtonText,
  isButtonDisabled,
  devMode = false,
}) => {
  if (!isOpen) return null;

  // Helper function to format amounts properly
  const formatAmount = (amount) => {
    if (!amount || amount === "0" || amount === "") return "0";

    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) return "0";

    // For very small amounts (less than 0.0001), show scientific notation
    if (numAmount < 0.0001) {
      return numAmount.toExponential(4);
    }
    // For small amounts (less than 0.01), show up to 8 decimals
    else if (numAmount < 0.01) {
      return parseFloat(numAmount.toFixed(8)).toString();
    }
    // For normal amounts (less than 1), show up to 6 decimals
    else if (numAmount < 1) {
      return parseFloat(numAmount.toFixed(6)).toString();
    }
    // For larger amounts, show up to 4 decimals
    else {
      return parseFloat(numAmount.toFixed(4)).toString();
    }
  };

  // Helper function to format USD values
  const formatUsdValue = (usdValue) => {
    if (!usdValue || usdValue === "0" || usdValue === "") return "0.00";

    const numValue = parseFloat(usdValue);
    if (isNaN(numValue)) return "0.00";

    return numValue.toFixed(2);
  };

  const handleStepClick = () => {
    if (devMode) {
      // In dev mode, allow clicking steps to change them
      // This would need to be passed as a prop
    }
  };

  return (
    <div className="transaction-review-overlay">
      <button onClick={onClose} className="transaction-close-button">
        ×
      </button>

      <div className="transaction-review-container">
        {/* Header */}
        <div className="transaction-review-header">
          <div className="transaction-review-title">Review limit</div>
          <div className="transaction-get-help">Get help</div>
        </div>

        {/* Token Display Section */}
        <div className="transaction-token-section-border">
          <div className="transaction-token-section">
            <div className="transaction-token-left">
              <div className="transaction-token-label">You pay</div>
            </div>
            <div className="transaction-token-right">
              <div className="transaction-token-right-inner">
                <div className="transaction-token-amount">
                  {formatAmount(fromAmount)} {fromToken?.symbol || "ETH"}
                </div>
                <div className="transaction-token-usd">
                  ≈ ${formatUsdValue(fromUsdValue)}
                </div>
              </div>
              <div className="transaction-token-icon">
                <img
                  src={
                    fromToken?.logoURI ||
                    fromToken?.logo_uri ||
                    "https://placehold.co/28x28"
                  }
                  alt={fromToken?.symbol || "Token"}
                />
              </div>
            </div>
          </div>

          {/* You Receive Section */}
          <div className="transaction-token-section t-b">
            <div className="transaction-token-left">
              <div className="transaction-token-label">You receive</div>
            </div>
            <div className="transaction-token-right">
              <div className="transaction-token-right-inner">
                <div className="transaction-token-amount">
                  {formatAmount(toAmount)} {toToken?.symbol || "AAVE"}
                </div>
                <div className="transaction-token-usd">
                  ≈ ${formatUsdValue(toUsdValue)}
                </div>
              </div>
              <div className="transaction-token-icon">
                <img
                  src={
                    toToken?.logoURI ||
                    toToken?.logo_uri ||
                    "https://placehold.co/28x28"
                  }
                  alt={toToken?.symbol || "Token"}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Approval Process Section */}
        <div className="transaction-approval-section">
          {/* Step 1: Approve in wallet - Only show if approval is needed */}
          {isApprovalNeeded && (
            <div
              className={`transaction-approval-step ${getStepState(1)}`}
              onClick={handleStepClick}
              style={{ cursor: devMode ? "pointer" : "default" }}
            >
              <div className="transaction-approval-step-icon">
                <img
                  src={
                    fromToken?.logoURI ||
                    fromToken?.logo_uri ||
                    "https://placehold.co/20x20"
                  }
                  alt="Token"
                  style={{
                    width: "28px",
                    height: "28px",
                    borderRadius: "50%",
                  }}
                />
              </div>
              <div className="transaction-approval-step-content">
                <div className="transaction-approval-step-title">
                  Approve in wallet
                </div>
                <div className="transaction-approval-help">
                  Why do I have to approve a token?
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Confirm swap */}
          <div
            className={`transaction-approval-step ${getStepState(2)}`}
            onClick={handleStepClick}
            style={{ cursor: devMode ? "pointer" : "default" }}
          >
            <div className="transaction-approval-step-icon">
              <span style={{ color: "white", fontSize: "16px" }}>✓</span>
            </div>
            <div className="transaction-approval-step-content">
              <div className="transaction-approval-step-title">
                Confirm swap
              </div>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <button
          onClick={onExecuteTransaction}
          disabled={isButtonDisabled()}
          className="transaction-action-button"
        >
          {getButtonText()}
        </button>
      </div>
    </div>
  );
};

export default TransactionModal;
