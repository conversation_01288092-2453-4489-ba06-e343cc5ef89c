async function getTokenBalance(chain_id, account, tokenAddress, authToken) {
  try {
    if (!chain_id || !account || !tokenAddress) {
      //  console.warn('Invalid or missing parameters for token balance');
      return null;
    }
    const response = await fetch("/api/rpc-call/get-token-balance", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`, // Send JWT token in the Authorization header
      },
      body: JSON.stringify({ chain_id, account, tokenAddress }),
    });

    const data = await response.json();
    if (response.ok) {
      console.log("Token Balance:", data.balance);
      return data.balance; // Return the balance for display or further use
    } else {
      console.warn("Error:", data.error);
    }
  } catch (error) {
    console.warn("Failed to fetch token balance:", error);
  }
}
export default getTokenBalance;
