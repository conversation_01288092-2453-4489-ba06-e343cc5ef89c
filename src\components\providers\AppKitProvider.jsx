"use client";

import React from "react";
import { AppKit } from "../../context/web3modal";

/**
 * AppKitProvider is a simple wrapper around the AppKit component.
 * The AppKit component handles initialization and error handling internally.
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The components to render
 * @returns {React.ReactNode} The wrapped application
 */
export default function AppKitProvider({ children }) {
  return <AppKit>{children}</AppKit>;
}
