import { NextResponse } from "next/server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
  console.error("JWT_SECRET is not defined in environment variables");
}

export async function POST(request) {
  try {
    if (!JWT_SECRET) {
      console.error("JWT_SECRET is not defined, authentication is disabled");
      return NextResponse.json(
        {
          valid: false,
          error: "Authentication is disabled (JWT_SECRET not configured)",
        },
        { status: 500 }
      );
    }

    const authHeader = request.headers.get("authorization");
    let token;

    if (authHeader && authHeader.startsWith("Bearer ")) {
      token = authHeader.split(" ")[1];
    } else {
      try {
        const contentType = request.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const clonedRequest = request.clone();

          const bodyText = await clonedRequest.text();
          if (bodyText && bodyText.trim() !== "") {
            const body = JSON.parse(bodyText);
            token = body.token;
          }
        }
      } catch (bodyError) {
        console.warn("Error parsing request body:", bodyError);
      }
    }

    if (!token) {
      return NextResponse.json(
        { valid: false, error: "No token provided" },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET);

    return NextResponse.json({
      valid: true,
      address: decoded.address,
    });
  } catch (error) {
    console.warn("Token verification error:", error);
    return NextResponse.json(
      { valid: false, error: "Invalid token" },
      { status: 401 }
    );
  }
}
