import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";
import erc20Abi from "../../../../constants/abis/erc20.json";

// RPC URLs for different chains
const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }

    const authenticatedAddress = authResult.address;

    const body = await request.json();
    const { chain_id, account, tokenAddress } = body;

    if (!chain_id || !account || !tokenAddress) {
      return NextResponse.json(
        { error: "Chain ID, account, and token address are required" },
        { status: 400 }
      );
    }

    console.log(
      `Fetching token balance for ${account} on chain ${chain_id} for token ${tokenAddress}`
    );

    const rpcUrl = RPC_URLS[chain_id];
    if (!rpcUrl) {
      return NextResponse.json(
        { error: "Unsupported chain ID" },
        { status: 400 }
      );
    }

    const provider = new ethers.JsonRpcProvider(rpcUrl);

    const tokenContract = new ethers.Contract(tokenAddress, erc20Abi, provider);

    const [balance, decimals, symbol] = await Promise.all([
      tokenContract.balanceOf(account),
      tokenContract.decimals(),
      tokenContract.symbol(),
    ]);

    console.log(`Token Balance for ${symbol}: ${balance.toString()}`);
    console.log(`Formatted Balance: ${ethers.formatUnits(balance, decimals)}`);

    return NextResponse.json({
      balance: balance.toString(),
      decimals: decimals.toString(),
      symbol,
      formattedBalance: ethers.formatUnits(balance, decimals),
      tokenBalance: balance.toString(),
    });
  } catch (error) {
    console.warn("RPC error:", error);
    return NextResponse.json(
      { error: "Failed to get token balance: " + error.message },
      { status: 500 }
    );
  }
}
