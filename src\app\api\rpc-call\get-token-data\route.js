import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";
import erc20Abi from "../../../../constants/abis/erc20.json";

// RPC URLs for different chains
const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }
    // Log the authenticated address for audit purposes
    console.log("Authenticated address:", authResult.address);
    const body = await request.json();
    const { chain_id, tokenAddress } = body;

    if (!chain_id || !RPC_URLS[chain_id] || !tokenAddress) {
      return NextResponse.json(
        { error: "Invalid or missing parameters" },
        { status: 400 }
      );
    }

    const provider = new ethers.JsonRpcProvider(RPC_URLS[chain_id]);

    const tokenContract = new ethers.Contract(tokenAddress, erc20Abi, provider);

    const [symbol, name, decimals] = await Promise.all([
      tokenContract.symbol(),
      tokenContract.name(),
      tokenContract.decimals(),
    ]);

    return NextResponse.json({
      chain_id,
      name,
      symbol,
      address: tokenAddress,
      decimals: typeof decimals === "bigint" ? Number(decimals) : decimals,
      logo_uri: `https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/${tokenAddress}/logo.png`,
    });
  } catch (error) {
    console.warn("Error fetching token data:", error);
    return NextResponse.json(
      { error: "Failed to fetch token data" },
      { status: 500 }
    );
  }
}
