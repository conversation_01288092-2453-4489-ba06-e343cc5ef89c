/** @type {import('next').NextConfig} */

// Load environment variables
require("dotenv").config({ path: ".env.local" });

const nextConfig = {
  reactStrictMode: true,
  productionBrowserSourceMaps: false,

  // Allow all origins in development mode
  allowedDevOrigins: [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://localhost:3002",
    "http://localhost:3003",
    "http://*************",
    "http://*************:3000",
    "http://*************:3001",
    "http://*************:3002",
    "http://*************:3003",
    "https://t.me",
    "https://telegram.org",
    "https://telegram.me",
  ],

  webpack: (config) => {
    config.resolve.fallback = {
      fs: false,
      net: false,
      tls: false,

      "pino-pretty": require.resolve("./src/utils/pino-pretty-mock.js"),
    };

    if (config.mode === "development") {
      config.devtool = false;
    }

    const cssRule = config.module.rules.find(
      (rule) => rule.test && rule.test.toString().includes(".css")
    );
    if (cssRule) {
      if (cssRule.use && Array.isArray(cssRule.use)) {
        cssRule.use.forEach((loader) => {
          if (loader.options && loader.options.sourceMap) {
            loader.options.sourceMap = false;
          }
        });
      }
    }

    return config;
  },

  distDir: ".next",

  pageExtensions: ["js", "jsx", "ts", "tsx"],

  compiler: {
    reactRemoveProperties: { properties: ["^(data-ssr)$"] },

    styledComponents: true,
  },

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "www.dextools.io",
      },
    ],
  },

  sassOptions: {
    includePaths: ["./node_modules"],
  },

  async redirects() {
    return [
      {
        source: "/home",
        destination: "/",
        permanent: true,
      },
    ];
  },

  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "X-Frame-Options",
            value: "SAMEORIGIN", // Allow embedding in same origin contexts
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          // CORS headers - allow all origins
          {
            key: "Access-Control-Allow-Origin",
            value: "*",
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS",
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "X-Requested-With, Content-Type, Authorization",
          },
          {
            key: "Access-Control-Allow-Credentials",
            value: "true",
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
