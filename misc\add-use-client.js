const fs = require("fs");
const path = require("path");

const directories = ["src/components", "src/context", "src/hooks"];

const extensions = [".js", ".jsx", ".ts", ".tsx"];

const skipIfContains = ['"use client"', "'use client'"];

const skipFiles = [
  "next.config.js",
  "package.json",
  "package-lock.json",
  "README.md",
  ".gitignore",
  ".eslintrc.json",
  "jsconfig.json",
  "tsconfig.json",
];

function addUseClientToFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");

    if (skipIfContains.some((str) => content.includes(str))) {
      console.log(`Skipping (already has 'use client'): ${filePath}`);
      return;
    }

    const newContent = `'use client';\n\n${content}`;
    fs.writeFileSync(filePath, newContent, "utf8");
    console.log(`Added 'use client' to: ${filePath}`);
  } catch (error) {
    console.warn(`Error processing file ${filePath}:`, error);
  }
}

function processDirectory(directory) {
  try {
    const items = fs.readdirSync(directory);

    for (const item of items) {
      const itemPath = path.join(directory, item);
      const stats = fs.statSync(itemPath);

      if (stats.isDirectory()) {
        processDirectory(itemPath);
      } else if (stats.isFile()) {
        const ext = path.extname(itemPath);
        const fileName = path.basename(itemPath);

        if (extensions.includes(ext) && !skipFiles.includes(fileName)) {
          addUseClientToFile(itemPath);
        }
      }
    }
  } catch (error) {
    console.warn(`Error processing directory ${directory}:`, error);
  }
}

for (const directory of directories) {
  console.log(`Processing directory: ${directory}`);
  processDirectory(directory);
}

console.log("Done adding 'use client' to component files.");
