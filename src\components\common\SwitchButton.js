"use client";

import React from "react";
import { useTheme } from "../../contexts/ThemeContext";

const SwitchCurrenciesButton = () => {
  const { isDarkMode } = useTheme();
  const containerStyle = {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    zIndex: 10,
    margin: 0,
    padding: 0,
  };

  const buttonStyle = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "36px",
    height: "36px",
    border: isDarkMode ? "none" : "1px solid #E0E0E0",
    background: isDarkMode
      ? "linear-gradient(95deg, #1b1d1f 0%, #151618 100%)" // Dark mode gradient
      : "#F5F5F5", // Light mode background
    borderRadius: "50%",
    cursor: "pointer",
    padding: 0,
  };

  const iconContainerStyle = {
    position: "relative",
    zIndex: 2,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    width: "36px",
    height: "36px",
  };

  return (
    <div style={containerStyle}>
      <button style={buttonStyle}>
        <div style={iconContainerStyle}>
          <svg
            width="43"
            height="43"
            viewBox="0 0 43 43"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            style={{ width: "36px", height: "36px" }}
          >
            <foreignObject x="-44" y="-44" width="131" height="131">
              <div
                xmlns="http://www.w3.org/1999/xhtml"
                style={{
                  backdropFilter: "blur(22px)",
                  clipPath: "url(#bgblur_0_1_7288_clip_path)",
                  height: "100%",
                  width: "100%",
                }}
              ></div>
            </foreignObject>
            <circle
              data-figma-bg-blur-radius="44"
              cx="21.5"
              cy="21.5"
              r="21"
              fill={isDarkMode ? "transparent" : "#F5F5F5"}
              stroke={isDarkMode ? "#333333" : "#E0E0E0"}
            />
            <path d="M21 9L21 33" stroke={isDarkMode ? "#FCE025" : "#808080"} />
            <path
              d="M28 26L21 33L14 26"
              stroke={isDarkMode ? "#FCE025" : "#808080"}
            />
            <path
              d="M28 16L21 8.99995L14 16"
              stroke={isDarkMode ? "#FCE025" : "#808080"}
            />
            <defs>
              <clipPath
                id="bgblur_0_1_7288_clip_path"
                transform="translate(44 44)"
              >
                <circle cx="21.5" cy="21.5" r="21" />
              </clipPath>
            </defs>
          </svg>
        </div>
      </button>
    </div>
  );
};

export default SwitchCurrenciesButton;
