export { metadata } from "./metadata";

if (typeof window !== "undefined") {
  window.process = window.process || {};
  window.process.env = window.process.env || {};
  window.process.env.NODE_ENV = "production";
}

import { Inter, Poppins } from "next/font/google";
import "../styles/globals.css";
import "../styles/loader.css";
import "../styles/about.css";
import "../styles/stake.css";
import "../styles/footer.css";
import "../styles/toast-custom.css";
import "../styles/hot-toast-custom.css"; // Custom styles for react-hot-toast
import "../styles/token-list-custom.css";
import "../styles/saver-info-custom.css";
import "../styles/content-transitions.css";
import ClientLayout from "./client-layout";
import HotToaster from "./HotToaster";

const inter = Inter({ subsets: ["latin"] });
const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
});

export default function RootLayout({ children }) {
  return (
    <html lang="en" className={`${poppins.variable}`}>
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </head>
      <body className={inter.className}>
        <ClientLayout fontClass={inter.className}>{children}</ClientLayout>
        <HotToaster />
      </body>
    </html>
  );
}
