import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";

const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }

    const authenticatedAddress = authResult.address;

    const body = await request.json();
    const { chain_id, account } = body;

    if (!chain_id || !account) {
      return NextResponse.json(
        { error: "Chain ID and account are required" },
        { status: 400 }
      );
    }

    console.log(`Fetching native balance for ${account} on chain ${chain_id}`);

    const rpcUrl = RPC_URLS[chain_id];
    if (!rpcUrl) {
      return NextResponse.json(
        { error: "Unsupported chain ID" },
        { status: 400 }
      );
    }

    const provider = new ethers.JsonRpcProvider(rpcUrl);

    const balance = await provider.getBalance(account);

    console.log(`Native Balance: ${balance.toString()}`);
    console.log(`Formatted Balance: ${ethers.formatEther(balance)}`);

    return NextResponse.json({
      balance: balance.toString(),
      formattedBalance: ethers.formatEther(balance),
    });
  } catch (error) {
    console.warn("RPC error:", error);
    return NextResponse.json(
      { error: "Failed to get balance: " + error.message },
      { status: 500 }
    );
  }
}
