import React, { useContext, useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { BlockchainContext } from "../../context/BlockchainContext";
import MeltAnimation from "../common/MeltAnimation";
import useIsMobile from "../../hooks/useIsMobile";

const TabsMode = () => {
  const { isJuicedMode, setIsJuicedMode } = useContext(BlockchainContext);
  const [showMeltAnimation, setShowMeltAnimation] = useState(false);
  const isMobile = useIsMobile();

  useEffect(() => {
    if (showMeltAnimation) {
      const timer = setTimeout(() => {
        setShowMeltAnimation(false);
      }, 4500);

      return () => clearTimeout(timer);
    }
  }, [showMeltAnimation]);

  console.log("TabsMode - isJuicedMode:", isJuicedMode);

  const handleFreshClick = () => {
    if (isJuicedMode) {
      console.log("Setting Fresh mode");
      setIsJuicedMode(false);
      localStorage.setItem("juiced_mode_enabled", "false");
    }
  };

  const handleJuicedClick = () => {
    if (!isJuicedMode) {
      console.log("Setting Juiced mode");
      setIsJuicedMode(true);
      localStorage.setItem("juiced_mode_enabled", "true");

      // Only show melt animation on desktop
      if (!isMobile) {
        setShowMeltAnimation(true);
      }
    }
  };

  const tabVariants = {
    inactive: {
      scale: 1,
      transition: { duration: 0.2 },
    },
    active: {
      scale: 1.05,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 15,
      },
    },
  };

  const gliderVariants = {
    left: {
      x: "0%",
      transition: {
        type: "spring",
        stiffness: 800,
        damping: 25,
      },
    },
    right: {
      x: "100%",
      transition: {
        type: "spring",
        stiffness: 800,
        damping: 25,
      },
    },
  };

  return (
    <>
      {/* Render MeltAnimation when showMeltAnimation is true */}
      {showMeltAnimation && <MeltAnimation />}

      <div className="tab-mode-container">
        <motion.div
          onClick={handleFreshClick}
          className={`tab-mode ${!isJuicedMode ? "tab-mode-active" : ""}`}
          variants={tabVariants}
          animate={!isJuicedMode ? "active" : "inactive"}
          whileHover={{ scale: !isJuicedMode ? 1.05 : 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          Fresh
        </motion.div>
        <motion.div
          onClick={handleJuicedClick}
          className={`tab-mode ${
            isJuicedMode ? "tab-mode-active juiced-mode-bg" : ""
          }`}
          variants={tabVariants}
          animate={isJuicedMode ? "active" : "inactive"}
          whileHover={{ scale: isJuicedMode ? 1.05 : 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          Juiced
        </motion.div>
        <motion.div
          className="glider"
          variants={gliderVariants}
          animate={isJuicedMode ? "right" : "left"}
        ></motion.div>
      </div>
    </>
  );
};

export default TabsMode;
