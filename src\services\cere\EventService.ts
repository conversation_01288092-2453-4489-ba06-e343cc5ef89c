"use client";

import { v4 as uuidv4 } from "uuid";

const CERE_CONFIG = {
  baseUrl: "https://ai-event.cere.io/",
  appId: "2105",
  appPubKey: "6RduSLUXdUbR4GQxM1Q9qQGz44vGMwXhdT9we9SkEGgsp8LS",
  dataServicePubKey:
    "0x876819ba5649913eea9e3082331313c6aa136e6485b3a9078469d7996f9695e9",
};

/**
 * Service for dispatching events to Cere Network
 */
class EventService {
  private eventDispatcher: any = null;
  private cereWalletSigner: any = null;
  private static instance: EventService | null = null;
  private walletAddress: string = "";

  constructor(private token: string) {
    if (EventService.instance) {
      return EventService.instance;
    }

    if (token && token !== "undefined" && token !== "null") {
      this.walletAddress = token;
    }

    EventService.instance = this;
  }

  /**
   * Initialize the EventService with Cere SDK
   */
  async init() {
    if (typeof window === "undefined") return;

    if (this.eventDispatcher) {
      console.log("EventService already initialized");
      return;
    }

    try {
      console.log("Initializing EventService with token:", this.token);

      const { EmbedWallet } = await import("@cere/embed-wallet");
      const { CereWalletSigner } = await import("@cere-activity-sdk/signers");
      const { EventDispatcher } = await import("@cere-activity-sdk/events");
      const { NoOpCipher } = await import("@cere-activity-sdk/ciphers");

      const embedWallet = new EmbedWallet();
      const authMethod = {
        type: "telegram-mini-app" as any,
        token: this.token,
      };
      embedWallet.init({
        authMethod,
        connectOptions: {
          permissions: {
            ed25519_signRaw: {
              title: "Sign your activity",
              description:
                "Allow the application to sign and send your activity on your behalf.",
            },
          },
        },
      });

      try {
        await embedWallet.isReady;
        await embedWallet.connect();
        console.log("Wallet connected successfully");

        this.cereWalletSigner = new CereWalletSigner(embedWallet);

        await this.cereWalletSigner.isReady();

        this.walletAddress = this.cereWalletSigner.address;
        console.log(
          "Wallet signer initialized with address:",
          this.walletAddress
        );

        console.log("Signer initialized with auto-signing permissions");
      } catch (error) {
        console.warn("Wallet initialization issue:", error);
      }

      const cipher = new NoOpCipher();
      const eventDispatcherParams = {
        appId: CERE_CONFIG.appId,
        connectionId: uuidv4(),
        sessionId: uuidv4(),
        baseUrl: CERE_CONFIG.baseUrl,
        appPubKey: CERE_CONFIG.appPubKey,
        dataServicePubKey: CERE_CONFIG.dataServicePubKey,
        eventTarget:
          typeof window !== "undefined" ? new EventTarget() : undefined,
      };

      this.eventDispatcher = new EventDispatcher(
        this.cereWalletSigner,
        cipher,
        eventDispatcherParams
      );

      if (this.eventDispatcher) {
        console.log("EventDispatcher successfully created");

        const methods = Object.getOwnPropertyNames(
          Object.getPrototypeOf(this.eventDispatcher)
        );
        console.log("EventDispatcher available methods:", methods);

        if (!methods.includes("getAllEvents")) {
          console.warn(
            "WARNING: getAllEvents method is not available on the EventDispatcher"
          );
          console.log(
            "This is expected with the current version of the Cere SDK"
          );
          console.log("The application will handle this gracefully");
        }

        if (!methods.includes("dispatchEvent")) {
          console.warn(
            "CRITICAL: dispatchEvent method is not available on the EventDispatcher"
          );
          console.warn("Event tracking will not work properly");
        }

        try {
          if (typeof this.cereWalletSigner.getPermissions === "function") {
            const signerPermissions =
              await this.cereWalletSigner.getPermissions();
            console.log("Signer permissions:", signerPermissions);

            const hasSignPermission =
              signerPermissions &&
              signerPermissions.ed25519_signRaw &&
              signerPermissions.ed25519_signRaw.granted;

            console.log("Has auto-signing permission:", hasSignPermission);

            if (!hasSignPermission) {
              console.warn(
                "Auto-signing permission not granted. Events may require manual signing."
              );
            }
          } else {
            console.log(
              "getPermissions method not available on signer, assuming permissions are granted"
            );
          }
        } catch (error) {
          console.warn("Could not verify signer permissions:", error);
        }
      } else {
        console.warn("EventDispatcher was not properly initialized");
      }

      console.log("EventService initialization complete");
    } catch (error) {
      console.warn("Error initializing EventService:", error);
    }
  }

  /**
   * Dispatch a wallet connect event to Cere
   * @param connectDetails - Details about the connection
   */
  async dispatchWalletConnectEvent(
    connectDetails: { account?: string; chainId?: number } = {}
  ) {
    if (typeof window === "undefined") return false;

    if (!this.eventDispatcher) {
      console.warn("Cannot dispatch wallet connect event: not initialized");
      return false;
    }

    try {
      const cere_wallet_id = CERE_CONFIG.appPubKey;
      const user_dapp_connected_wallet =
        connectDetails.account || "unknown-wallet";
      const chain_id = connectDetails.chainId || 0;

      let referenceId = localStorage.getItem("referenceId");

      if (!referenceId) {
        referenceId = uuidv4();
      }

      console.log("Preparing WALLET_CONNECT event");
      console.log("Cere Wallet ID:", cere_wallet_id);
      console.log("User Dapp Connected Wallet:", user_dapp_connected_wallet);
      console.log("Chain ID:", chain_id);
      console.log("Reference ID:", referenceId);

      const { ActivityEvent } = await import("@cere-activity-sdk/events");
      const eventPayload = {
        cere_wallet_id,
        user_dapp_connected_wallet,
        chain_id,
        reference_id: referenceId,
        timestamp: new Date().toISOString(),
      };

      console.log("Event payload:", eventPayload);
      const event = new ActivityEvent("WALLET_CONNECT", eventPayload);

      console.log("Dispatching event to Cere Network...");
      const result = await this.eventDispatcher.dispatchEvent(event);
      console.log("Dispatch result:", result);
      console.log("Successfully dispatched WALLET_CONNECT event");
      return true;
    } catch (error) {
      console.warn("Error dispatching wallet connect event:", error);
      console.warn(
        "Error details:",
        JSON.stringify(error, Object.getOwnPropertyNames(error))
      );
      return false;
    }
  }

  /**
   * Dispatch a swap event to Cere
   */
  async dispatchSwapEvent(swapDetails: any) {
    if (typeof window === "undefined") return false;

    if (!this.eventDispatcher) {
      console.warn("Cannot dispatch swap event: not initialized");
      return false;
    }

    try {
      const { ActivityEvent } = await import("@cere-activity-sdk/events");

      let referenceId =
        swapDetails.referenceId || localStorage.getItem("referenceId");

      if (!referenceId) {
        referenceId = "N/A";
      }

      const formattedPayload = {
        cere_wallet_id: CERE_CONFIG.appPubKey,
        chain_id: swapDetails.chainId || 0,
        timestamp: new Date().toISOString(),
        reference_id: referenceId,
        tx_hash: swapDetails.txHash || "",

        user_dapp_connected_wallet:
          swapDetails.user_dapp_connected_wallet ||
          swapDetails.walletId ||
          this.walletAddress ||
          "unknown-wallet",

        token_pair: swapDetails.tokenPair || "",

        sell_token_address: swapDetails.fromTokenId || "",
        sell_token_amount: swapDetails.fromAmount || "0",

        buy_token_address: swapDetails.toTokenId || "",
        buy_token_amount: swapDetails.toAmount || "0",
      };

      console.log("Preparing DEX_SWAP event");
      console.log("Cere Wallet ID:", formattedPayload.cere_wallet_id);
      console.log("Reference ID:", formattedPayload.reference_id);
      console.log(
        "Formatted payload:",
        JSON.stringify(formattedPayload, null, 2)
      );

      const event = new ActivityEvent("DEX_SWAP", formattedPayload);

      console.log("Dispatching event to Cere Network...");
      const result = await this.eventDispatcher.dispatchEvent(event);
      console.log("Dispatch result:", result);
      console.log("Successfully dispatched DEX_SWAP event");

      return true;
    } catch (error) {
      console.warn("Error dispatching swap event:", error);
      console.warn(
        "Error details:",
        JSON.stringify(error, Object.getOwnPropertyNames(error))
      );
      return false;
    }
  }

  /**
   * Retrieve all stored events
   */
  async getAllEvents() {
    if (typeof window === "undefined") return [];

    if (!this.eventDispatcher) {
      console.warn("Cannot retrieve events: not initialized");
      return [];
    }

    try {
      console.log("Retrieving all stored events...");

      if (typeof this.eventDispatcher.getAllEvents === "function") {
        const events = await this.eventDispatcher.getAllEvents();
        console.log(`Retrieved ${events.length} events`);
        return events;
      } else {
        console.warn("getAllEvents method not available on eventDispatcher");

        if (
          this.eventDispatcher.events &&
          Array.isArray(this.eventDispatcher.events)
        ) {
          console.log(
            `Retrieved ${this.eventDispatcher.events.length} events from events property`
          );
          return this.eventDispatcher.events;
        }

        console.log("No events could be retrieved, returning empty array");
        return [];
      }
    } catch (error) {
      console.warn("Error retrieving events:", error);
      return [];
    }
  }

  /**
   * Check if the event service is properly initialized
   */
  isInitialized() {
    const initialized = !!this.eventDispatcher && !!this.cereWalletSigner;
    console.log("EventService initialization status:", initialized);

    if (initialized) {
      console.log("Wallet address:", this.walletAddress);
    }

    return initialized;
  }
}

export default EventService;
