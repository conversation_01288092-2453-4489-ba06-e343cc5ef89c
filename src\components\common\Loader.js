'use client';

function Loader({ size = "medium", className = "" }) {
  // Size variants
  const sizeClasses = {
    small: "loader-small",
    medium: "loader-medium",
    large: "loader-large",
  };

  const sizeClass = sizeClasses[size] || sizeClasses.medium;

  return (
    <div className={`loader-wrapper ${className}`}>
      <div className={`loader ${sizeClass}`}></div>
    </div>
  );
}

export default Loader;
