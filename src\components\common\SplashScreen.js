"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";
import { useTheme } from "../../contexts/ThemeContext";

const SplashScreen = ({ onLoadComplete = () => {}, fadeStarted = false }) => {
  const { isDarkMode } = useTheme();
  const [progress, setProgress] = useState(0);
  const [particles, setParticles] = useState([]);
  const [fadeOut, setFadeOut] = useState(false);
  const [particleFadeOut, setParticleFadeOut] = useState(false);

  useEffect(() => {
    if (fadeStarted) {
      setFadeOut(true);

      setTimeout(() => {
        setParticleFadeOut(true);
      }, 1000);
    }
  }, [fadeStarted]);

  useEffect(() => {
    if (progress === 100) {
      onLoadComplete();
    }
  }, [progress, onLoadComplete]);

  useEffect(() => {
    const newParticles = [];
    for (let i = 0; i < 50; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: Math.random() * 5 + 1,
        opacity: Math.random() * 0.6 + 0.1,
        speed: Math.random() * 1.5 + 0.5,
      });
    }
    setParticles(newParticles);
  }, []);

  useEffect(() => {
    if (progress === 100) {
      if (onLoadComplete) onLoadComplete();
    }
  }, [progress, onLoadComplete]);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev + 2;
        return newProgress <= 100 ? newProgress : 100;
      });
    }, 30);

    return () => clearInterval(interval);
  }, []);

  return (
    <div
      style={{
        ...styles.splashScreen,
        backgroundColor: fadeOut ? "transparent" : "#0a0a0a",
        transition: "background-color 0.5s ease-out",
        pointerEvents: fadeOut ? "none" : "auto",
      }}
    >
      {/* Particles */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          style={{
            ...styles.particle,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            opacity: particleFadeOut ? 0 : particle.opacity,
            backgroundColor: particleFadeOut
              ? "transparent"
              : "rgba(252, 224, 37, 0.3)",
            boxShadow: particleFadeOut
              ? "none"
              : "0 0 6px rgba(252, 224, 37, 0.5)",
            animation: `float ${particle.speed}s infinite alternate ease-in-out`,
            transition:
              "opacity 3s ease-out, background-color 3s ease-out, box-shadow 3s ease-out",
          }}
        />
      ))}

      <div
        style={{
          ...styles.splashLogo,
          opacity: fadeOut ? 0 : 1,
          transition: "opacity 1s ease-out",
        }}
      >
        <div style={styles.logoContainer}>
          <Image
            src={
              isDarkMode ? "/logos/lite-logo-light.svg" : "/logos/lite-logo.svg"
            }
            alt="Papple Logo"
            width={180}
            height={180}
            style={styles.logoImage}
            className="splash-logo"
            priority={true}
          />
        </div>
        <div style={styles.progressContainer}>
          <div style={{ ...styles.progressFill, width: `${progress}%` }}></div>
        </div>
      </div>

      <style jsx global>{`
        @keyframes float {
          0% {
            transform: translateY(0px) rotate(0deg);
            filter: brightness(1);
          }
          50% {
            filter: brightness(1.2);
          }
          100% {
            transform: translateY(15px) rotate(5deg);
            filter: brightness(0.8);
          }
        }
        @keyframes pulse {
          0% {
            transform: scale(1);
            opacity: 0.8;
            filter: brightness(0.9);
          }
          50% {
            transform: scale(1.05);
            opacity: 1;
            filter: brightness(1.1);
          }
          100% {
            transform: scale(1);
            opacity: 0.8;
            filter: brightness(0.9);
          }
        }
        @keyframes rotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

const styles = {
  splashScreen: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#0a0a0a",
    overflow: "hidden",
  },
  splashLogo: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 2,
  },
  logoContainer: {
    position: "relative",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: "40px",
    animation: "pulse 3s infinite ease-in-out",
  },
  logoImage: {
    objectFit: "contain",
    animation: "pulse 3s infinite ease-in-out",
    filter: "drop-shadow(0 0 20px rgba(252, 224, 37, 0.9))",
    zIndex: 2,
    position: "relative",
    transform: "scale(1.1)",
  },
  progressContainer: {
    width: "240px",
    height: "4px",
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderRadius: "4px",
    overflow: "hidden",
    marginBottom: "15px",
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#FCE025",
    borderRadius: "4px",
    transition: "width 0.2s ease-out",
    boxShadow: "0 0 10px rgba(252, 224, 37, 0.7)",
  },
  loadingText: {
    fontFamily: "Arial, sans-serif",
    fontSize: "14px",
    color: "rgba(255, 255, 255, 0.6)",
    marginTop: "5px",
    letterSpacing: "1px",
  },
  particle: {
    position: "absolute",
    backgroundColor: "rgba(252, 224, 37, 0.3)",
    borderRadius: "50%",
    pointerEvents: "none",
    zIndex: 1,
    boxShadow: "0 0 6px rgba(252, 224, 37, 0.5)",
    filter: "blur(1px)",
  },
};

export default SplashScreen;
