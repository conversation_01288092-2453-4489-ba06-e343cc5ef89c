import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";

// RPC URLs for different chains
const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }

    const authenticatedAddress = authResult.address;

    const body = await request.json();
    const { chain_id } = body;

    if (!chain_id || !RPC_URLS[chain_id]) {
      return NextResponse.json(
        { error: "Invalid or missing chain_id" },
        { status: 400 }
      );
    }

    const provider = new ethers.JsonRpcProvider(RPC_URLS[chain_id]);

    const feeData = await provider.getFeeData();

    const serializedFeeData = {
      gasPrice: feeData.gasPrice ? feeData.gasPrice.toString() : null,
      maxFeePerGas: feeData.maxFeePerGas
        ? feeData.maxFeePerGas.toString()
        : null,
      maxPriorityFeePerGas: feeData.maxPriorityFeePerGas
        ? feeData.maxPriorityFeePerGas.toString()
        : null,
    };

    return NextResponse.json({ feeData: serializedFeeData });
  } catch (error) {
    console.warn("Error fetching fee data:", error);
    return NextResponse.json(
      { error: "Failed to fetch fee data" },
      { status: 500 }
    );
  }
}
