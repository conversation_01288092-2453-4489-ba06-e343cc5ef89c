"use client";

import { Telegram, Twitter, Globe } from "../common/SVGMAIN.js";
import useIsMobile from "../../hooks/useIsMobile";
import { useState, useEffect } from "react";
import Link from "next/link";

function FooterBar() {
  const isMobile = useIsMobile();
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());

  // Update the year if the component stays mounted across year boundaries
  useEffect(() => {
    const interval = setInterval(() => {
      const year = new Date().getFullYear();
      if (year !== currentYear) {
        setCurrentYear(year);
      }
    }, 1000 * 60 * 60); // Check once per hour

    return () => clearInterval(interval);
  }, [currentYear]);

  return (
    <div className="footer-container">
      <div className="nav-left">
        {/*         <div className="footer-left">
          <span className="footer-tagline">The Freshest way to trade</span>
          {!isMobile && (
            <span className="footer-copyright">
              &copy; {currentYear} PineappleDex. All rights reserved.
            </span>
          )}
        </div> */}
      </div>
      <div className="nav-center">
        Do you need help before proceeding?{" "}
        <Link href="#" className="click-here-footer">
          Click here
        </Link>
      </div>
      <div className="nav-right">
        {/*         <div className="footer-social-icons">
          <a
            href="https://t.me/PineappleDexNews"
            target="_blank"
            rel="noopener noreferrer"
            className="footer-icon-link"
            title="Announcements"
          >
            <div className="footer-icon">
              <Telegram />
            </div>
          </a>
          <a
            href="https://x.com/PineappleDex"
            target="_blank"
            rel="noopener noreferrer"
            className="footer-icon-link"
            title="Twitter"
          >
            <div className="footer-icon">
              <Twitter />
            </div>
          </a>
          <a
            href="https://pineappledex.com"
            target="_blank"
            rel="noopener noreferrer"
            className="footer-icon-link"
            title="Website"
          >
            <div className="footer-icon">
              <Globe />
            </div>
          </a>

          {process.env.NODE_ENV === "development" && (
            <a
              href="/log-viewer"
              target="_blank"
              rel="noopener noreferrer"
              className="footer-icon-link dev-link"
              title="Log Viewer"
            >
              <div className="footer-icon">
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3 5h18M3 12h18M3 19h18"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </div>
            </a>
          )}
        </div>

        {isMobile && (
          <div className="footer-copyright-mobile">
            &copy; {currentYear} PineappleDex
          </div>
        )} */}
        <div className="block-text">
          20211834
          <div
            style={{
              background: "#78FF8D",
              width: "12px",
              height: "12px",
              borderRadius: "50%",
              margin: "0 5px",
            }}
          ></div>
        </div>
      </div>
    </div>
  );
}

export default FooterBar;
