"use client";

import customToast from "../../utils/toast";

export default function ToastTester() {
  const showSuccessToast = () => {
    customToast.success("Success toast is working!");
  };

  const showErrorToast = () => {
    customToast.error("Error toast is working!");
  };

  const showInfoToast = () => {
    customToast.info("Please connect your wallet");
  };

  const showWarningToast = () => {
    customToast.warning("Warning toast is working!");
  };

  const showLoadingToast = () => {
    customToast.loading("Loading toast is working!");
  };

  return (
    <div
      style={{
        position: "fixed",
        bottom: "20px",
        right: "20px",
        zIndex: 1000,
        display: "flex",
        flexDirection: "column",
        gap: "10px",
      }}
    >
      <button
        onClick={showSuccessToast}
        style={{
          padding: "10px 20px",
          background: "#1a8754",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: "pointer",
        }}
      >
        Success Toast
      </button>
      <button
        onClick={showErrorToast}
        style={{
          padding: "10px 20px",
          background: "#dc3545",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: "pointer",
        }}
      >
        Error Toast
      </button>
      <button
        onClick={showLoadingToast}
        style={{
          padding: "10px 20px",
          background: "#0d6efd",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: "pointer",
        }}
      >
        Loading Toast
      </button>
      <button
        onClick={showInfoToast}
        style={{
          padding: "10px 20px",
          background: "#0d6efd",
          color: "white",
          border: "none",
          borderRadius: "4px",
          cursor: "pointer",
        }}
      >
        Info Toast
      </button>
      <button
        onClick={showWarningToast}
        style={{
          padding: "10px 20px",
          background: "#ffc107",
          color: "black",
          border: "none",
          borderRadius: "4px",
          cursor: "pointer",
        }}
      >
        Warning Toast
      </button>
    </div>
  );
}
