// Clean QuoteDisplay component - only handles UI
import React from "react";

const QuoteDisplay = ({
  account,
  swapData,
  loading,
  error,
  sellAmount,
  buyAmount,
  isApprovalNeeded,
  onApproveClick,
  onSwapClick,
}) => {
  // Loading state
  /*   if (loading) {
    return (
      <div className="swap-button disable">
        Loading quote...
      </div>
    );
  } */

  // Error state
  if (error) {
    return <div className="swap-button disable">{error}</div>;
  }

  // No account
  if (!account) {
    return <div className="swap-button">Connect Wallet</div>;
  }

  // No amount entered
  if (!sellAmount || sellAmount === 0 || sellAmount === "0") {
    return <div className="swap-button disable">Enter an amount</div>;
  }

  // No swap data available
  if (!swapData) {
    return <div className="swap-button disable">No quote available</div>;
  }

  // Show appropriate button based on approval status
  if (isApprovalNeeded) {
    return (
      <div className="swap-button" onClick={onApproveClick}>
        Approve
      </div>
    );
  }

  return (
    <div className="swap-button" onClick={onSwapClick}>
      Swap
    </div>
  );
};

export default QuoteDisplay;
