body {
  animation: gradientBackground 15s ease infinite;
}

@keyframes gradientBackground {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

h1 {
  margin-bottom: 30px;
  font-weight: 600;
  letter-spacing: 1px;
  text-align: center;
}

.loader {
  margin: 20px;
  position: relative;
}

/* Loader 1: Spinning Circle */

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loader 2: Bouncing Dots */

.loader2 div {
  width: 15px;
  height: 15px;
  background-color: #3498db;
  border-radius: 50%;
  animation: bounce 1.5s infinite ease-in-out;
  box-shadow: 0 0 15px rgba(52, 152, 219, 0.6);
}

.loader2 div:nth-child(2) {
  animation-delay: -0.3s;
}

.loader2 div:nth-child(3) {
  animation-delay: -0.6s;
}

@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Loader 3: Growing Circle */

@keyframes grow-shrink {
  0%,
  100% {
    transform: scale(0);
  }
  50% {
    transform: scale(1);
  }
}

/* Loader 4: Rotating Square */

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loader 5: Fading Dots */

.loader5 div {
  width: 15px;
  height: 15px;
  background-color: #3498db;
  border-radius: 50%;
  animation: fade 1.2s infinite ease-in-out both;
  box-shadow: 0 0 15px rgba(52, 152, 219, 0.6);
}

.loader5 div:nth-child(2) {
  animation-delay: -0.4s;
}

.loader5 div:nth-child(3) {
  animation-delay: -0.8s;
}

@keyframes fade {
  0%,
  39%,
  100% {
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
}

/* Loader 6: Pulsing Dot */

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
}

/* Loader 7: Double Ring */

@keyframes spin-reverse {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

/* Loader 9: Loading Bars */

@keyframes loading-bar {
  0%,
  100% {
    transform: scaleX(0);
  }
  50% {
    transform: scaleX(1);
  }
}

/* Loader 10: Infinity Loop */

@keyframes loop {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loader 11: Spinning Loader (Updated) */
/* This has been replaced by the Loader component */
