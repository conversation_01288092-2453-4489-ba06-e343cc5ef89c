import { useState, useEffect, useContext, useRef, useMemo } from "react";
import { BlockchainContext } from "../../context/BlockchainContext";
import {
  Settings,
  Medium,
  Telegram,
  Twitter,
  YouTube,
  Refresh,
  SaverInfoIcon,
  DownArrow,
  SlippageIcon,
} from "../common/SVGMAIN.js";
import { ethers } from "ethers";
import { toast } from "react-hot-toast";
import TabsMode from "./TabsMode";
import { motion } from "framer-motion";
import useIsMobile from "../../hooks/useIsMobile";

function SwapSettings({
  setShowChartState,
  showChart,
  setShowAudits,
  showAudits,
}) {
  const {
    savedData,
    updateSavedData,
    updateData,
    savedPriorityGas,
    savedSlippage,
    useAutoGas,
    useAutoSlippage,
    savedAddedPriority,
    providerHTTP,
    authToken,
    chain_id,
    account,
  } = useContext(BlockchainContext);
  const [showSettings, setShowSettings] = useState(false);
  const Settingsicon = useRef(null);
  const isMobile = useIsMobile();

  /*   function CurrentGwei() {
    const GWEI = useRef(0);
    const [CurrentGwei, setCurrentGwei] = useState(0);

    async function getGasFees() {
      if (!authToken || !account) {
        return;
      }
      try {
        async function getNonEthGasFees() {
          try {
            async function fetchFeeData(chain_id) {
              try {
                const response = await fetch("/api/rpc-call/get-fee-data", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authToken}`, 
                  },
                  body: JSON.stringify({ chain_id }),
                });

                const data = await response.json();

                if (response.ok) {
                  console.log("Fee Data:", data.feeData);
                  return data.feeData;
                } else {
                  console.warn("Error:", data.error);
                }
              } catch (error) {
                console.warn("Failed to fetch fee data:", error);
              }
            }
            const feeData = await fetchFeeData(chain_id);
            
            const zeroPointOneGwei = BigInt(2000000000);

            
            const gasPrice = BigInt(feeData.gasPrice || 0);

            
            const maxPriorityFeePerGas = zeroPointOneGwei;

            
            const maxFeePerGas = maxPriorityFeePerGas + gasPrice;

            

            
            return {
              gasPrice: maxFeePerGas.toString(),
              maxPriorityFeePerGas: maxPriorityFeePerGas.toString(),
              maxFeePerGas: maxFeePerGas.toString(),
            };
          } catch (error) {
            console.warn("Failed to getNonEthGasFees:", error);
          }
        }

        const feeData = await getNonEthGasFees();
        console.log("feeData", feeData);
        const baseFeePerGas = feeData.gasPrice;
        

        const baseFeePerGasGwei = ethers.formatUnits(
          baseFeePerGas?.toString(),
          "gwei"
        );
        
        if (baseFeePerGasGwei !== GWEI.current) {
          GWEI.current = Number(baseFeePerGasGwei).toFixed(2);
          
          let currentFee = Number(baseFeePerGasGwei).toFixed(2);
          if (currentFee > 50) {
            currentFee = `${GWEI.current} High`;
          } else if (currentFee > 20) {
            currentFee = `${GWEI.current} High`;
          } else if (currentFee > 10) {
            currentFee = `${GWEI.current} Average`;
          } else {
            currentFee = `${GWEI.current} Low`;
          }

          setCurrentGwei(currentFee);
        }
      } catch (error) {}
    }

    useEffect(() => {
      const interval = setInterval(() => {
        getGasFees();
      }, 30000);
      getGasFees();

      return () => clearInterval(interval);
    }, []);

    if (!authToken || !account) {
      return <></>;
    }
    return <div className="gwei-info"> Network Gas: {CurrentGwei}</div>;
  }

  const memoCurrentGwei = useMemo(() => <CurrentGwei />, [account]); */

  const GasSlipComponent = () => {
    const [slippage, setSlippage] = useState(savedSlippage.current || "auto");
    const [addedPriority, setAddedPriority] = useState(
      savedAddedPriority.current || "auto"
    );

    const handleAddedPriorityChange = (value) => {
      setAddedPriority(value);

      updateData("savedAddedPriority", value);

      if (value === "auto") {
        updateData("priorityGas", "2");
      } else {
        updateData("priorityGas", value.toString());
      }

      console.log("Gas priority updated to:", value);
    };

    const handleSlippageChange = (value) => {
      setSlippage(value);

      const slippageValue = value === "auto" ? 6 : parseFloat(value);
      updateData("savedSlippage", value);

      updateData("savedSlippageValue", slippageValue);
    };

    useEffect(() => {
      const iframeContainer = document.querySelector(".swap-upper");
      iframeContainer.style.opacity = 0.2;

      setTimeout(() => {
        iframeContainer.style.opacity = 1;
      }, 10);
    }, []);

    const containerRef = useRef(null);

    const handleClickOutside = (event) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target)
      ) {
        if (
          Settingsicon.current &&
          !Settingsicon.current.contains(event.target)
        ) {
          setShowSettings(false);
        }
      }
    };

    useEffect(() => {
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, []);
    const [isSlippageOpen, setIsSlippageOpen] = useState(false);
    const [isGasOpen, setIsGasOpen] = useState(false);

    const slippageRef = useRef(null);
    const gasRef = useRef(null);

    const slippageOptions = [
      { value: "auto", label: "Auto" },
      { value: "2", label: "2%" },
      { value: "4", label: "4%" },
      { value: "6", label: "6%" },
      { value: "8", label: "8%" },
      { value: "10", label: "10%" },
    ];
    const gasOptions = [
      { value: "auto", label: "Auto" },
      { value: 2, label: "Average" },
      { value: 3, label: "Quick" },
      { value: 5, label: "Instant" },
    ];

    useEffect(() => {
      const handleClickOutside = (e) => {
        if (slippageRef.current && !slippageRef.current.contains(e.target)) {
          setIsSlippageOpen(false);
        }
        if (gasRef.current && !gasRef.current.contains(e.target)) {
          setIsGasOpen(false);
        }
      };
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    useEffect(() => {
      if (savedSlippage.current !== slippage) {
        setSlippage(savedSlippage.current);
      }
      if (savedAddedPriority.current !== addedPriority) {
        setAddedPriority(savedAddedPriority.current);
      }
    }, [showSettings]);

    return (
      <div className="swap-upper" ref={containerRef}>
        {/* Slippage Box */}
        <div className="swap-upper-box">
          <div className="auto-container">
            <div className="us-ts">
              <div className="settings-icon-container">
                <SlippageIcon />
              </div>
              Slippage
            </div>
            {/* Div‑based dropdown for slippage */}
            <div className="dropdown-wrapper" ref={slippageRef}>
              <div
                className="dropdown-header"
                onClick={() => setIsSlippageOpen((prev) => !prev)}
              >
                <span>
                  {slippageOptions.find((opt) => opt.value === slippage)
                    ?.label || (slippage === "auto" ? "Auto" : `${slippage}%`)}
                </span>
                <div
                  className={`dropdown-arrow ${isSlippageOpen ? "open" : ""}`}
                >
                  <DownArrow />
                </div>
              </div>
              {isSlippageOpen && (
                <div className="dropdown-options">
                  {slippageOptions.map((option) => (
                    <div
                      key={option.value}
                      className="dropdown-option"
                      onClick={() => {
                        handleSlippageChange(option.value);
                        setIsSlippageOpen(false);
                      }}
                    >
                      {option.label}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Gas Box */}
        <div className="swap-upper-box">
          <div className="auto-container">
            <div className="us-ts">
              <div className="settings-icon-container">
                <SaverInfoIcon />
              </div>
              Gas
            </div>
            {/* Div‑based dropdown for gas */}
            <div className="dropdown-wrapper" ref={gasRef}>
              <div
                className="dropdown-header"
                onClick={() => setIsGasOpen((prev) => !prev)}
              >
                <span>
                  {gasOptions.find((opt) => opt.value === addedPriority)
                    ?.label ||
                    (addedPriority === "auto"
                      ? "Auto"
                      : addedPriority === "slow"
                      ? "Slow"
                      : addedPriority === "normal"
                      ? "Normal"
                      : addedPriority === "fast"
                      ? "Fast"
                      : addedPriority === "instant"
                      ? "Instant"
                      : `Custom (${addedPriority})`)}
                </span>
                <div className={`dropdown-arrow ${isGasOpen ? "open" : ""}`}>
                  <DownArrow />
                </div>
              </div>
              {isGasOpen && (
                <div className="dropdown-options">
                  {gasOptions.map((option) => (
                    <div
                      key={option.value}
                      className="dropdown-option"
                      onClick={() => {
                        handleAddedPriorityChange(option.value);
                        setIsGasOpen(false);
                      }}
                    >
                      {option.label}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const { isJuicedMode } = useContext(BlockchainContext);

  const JuicedModeTabs = () => {
    const [activeTab, setActiveTab] = useState("Swap");
    const [previousTab, setPreviousTab] = useState(null);

    const handleTabClick = (tab) => {
      if (tab !== activeTab) {
        setPreviousTab(activeTab);
        setActiveTab(tab);
        console.log(`Switched to ${tab} tab in JuicedMode`);
      }
    };

    const containerVariants = {
      initial: {
        opacity: 0,
        scale: 0.9,
      },
      animate: {
        opacity: 1,
        scale: 1,
        transition: {
          duration: 0.4,
          ease: "easeOut",
          staggerChildren: 0.1,
        },
      },
      exit: {
        opacity: 0,
        scale: 0.9,
        transition: {
          duration: 0.3,
        },
      },
    };

    const tabVariants = {
      inactive: {
        scale: 1,
        color: "rgba(255, 255, 255, 0.7)",
        transition: {
          duration: 0.3,
          ease: "easeOut",
        },
      },
      active: {
        scale: 1.05,
        color: "#000000",
        transition: {
          type: "spring",
          stiffness: 400,
          damping: 15,
          mass: 0.8,
        },
      },
      hover: (isActive) => ({
        scale: isActive ? 1.05 : 1.02,
        y: -2,
        transition: {
          duration: 0.2,
          ease: "easeOut",
        },
      }),
      tap: {
        scale: 0.98,
        transition: {
          duration: 0.1,
        },
      },
    };

    const gliderVariants = {
      swap: {
        x: 2,
        transition: {
          type: "spring",
          stiffness: 500,
          damping: 30,
          mass: 1,
        },
      },
      limit: {
        x: 64,
        transition: {
          type: "spring",
          stiffness: 500,
          damping: 30,
          mass: 1,
        },
      },
      slice: {
        x: 126,
        transition: {
          type: "spring",
          stiffness: 500,
          damping: 30,
          mass: 1,
        },
      },
    };

    const textVariants = {
      inactive: {
        y: 0,
        transition: {
          duration: 0.2,
        },
      },
      active: {
        y: 0,
        fontWeight: 600,
        transition: {
          duration: 0.2,
        },
      },
    };

    return (
      <motion.div
        className="juiced-tabs-container"
        variants={containerVariants}
        initial="initial"
        animate="animate"
        exit="exit"
        layout
      >
        <motion.div
          className="juiced-glider"
          variants={gliderVariants}
          animate={activeTab.toLowerCase()}
          layoutId="juiced-glider"
        ></motion.div>

        <motion.div
          className={`swap-tab juiced-tab ${
            activeTab === "Swap" ? "swap-tab-active" : ""
          }`}
          onClick={() => handleTabClick("Swap")}
          variants={tabVariants}
          animate={activeTab === "Swap" ? "active" : "inactive"}
          whileHover="hover"
          whileTap="tap"
          custom={activeTab === "Swap"}
          layout
        >
          <motion.span
            variants={textVariants}
            animate={activeTab === "Swap" ? "active" : "inactive"}
          >
            Swap
          </motion.span>
        </motion.div>

        <motion.div
          className={`swap-tab juiced-tab  ${
            activeTab === "Limit" ? "swap-tab-active" : ""
          } no-click`}
          onClick={() => handleTabClick("Limit")}
          variants={tabVariants}
          animate={activeTab === "Limit" ? "active" : "inactive"}
          whileHover="hover"
          whileTap="tap"
          custom={activeTab === "Limit"}
          layout
        >
          <motion.span
            variants={textVariants}
            animate={activeTab === "Limit" ? "active" : "inactive"}
          >
            Limit
          </motion.span>
        </motion.div>

        <motion.div
          className={`swap-tab juiced-tab ${
            activeTab === "Slice" ? "swap-tab-active" : ""
          } no-click`}
          onClick={() => handleTabClick("Slice")}
          variants={tabVariants}
          animate={activeTab === "Slice" ? "active" : "inactive"}
          whileHover="hover"
          whileTap="tap"
          custom={activeTab === "Slice"}
          layout
        >
          <motion.span
            variants={textVariants}
            animate={activeTab === "Slice" ? "active" : "inactive"}
          >
            Slice
          </motion.span>
        </motion.div>
      </motion.div>
    );
  };

  return (
    <div className="swap-icons-container">
      {showSettings && <GasSlipComponent />}

      {/* Left side - Desktop tabs */}
      {!isMobile && (
        <div className="swap-icons-left">
          <div className="swap-tabs-container">
            <TabsMode />
          </div>
        </div>
      )}

      <div className="swap-icons-right">
        {/*         {!isMobile && isJuicedMode && <JuicedModeTabs />}
         */}{" "}
        {/*         {!isMobile && !isJuicedMode && (
         */}{" "}
        <div
          className="swap-icon"
          ref={Settingsicon}
          onClick={() => setShowSettings(!showSettings)}
        >
          <Settings />
        </div>
        {/*         )}
         */}{" "}
      </div>
    </div>
  );
}

export default SwapSettings;
