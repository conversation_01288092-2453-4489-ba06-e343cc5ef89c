"use client";

import React, { useState, useEffect, useContext, useRef } from "react";
import { BlockchainContext } from "../../context/BlockchainContext";
import { motion, AnimatePresence } from "framer-motion";
import { SlippageIcon, SaverInfoIcon } from "../common/SVGMAIN";
import { toast } from "react-hot-toast";

const JuicedModeSettings = () => {
  const { updateData, savedSlippage, savedAddedPriority } =
    useContext(BlockchainContext);

  const [slippage, setSlippage] = useState(savedSlippage.current || "auto");
  const [addedPriority, setAddedPriority] = useState(
    savedAddedPriority.current || "auto"
  );
  const [showCustomSlippage, setShowCustomSlippage] = useState(false);
  const [showCustomGas, setShowCustomGas] = useState(false);
  const [customSlippageValue, setCustomSlippageValue] = useState("2.0");
  const [customGasValue, setCustomGasValue] = useState("10");

  const customSlippageRef = useRef(null);
  const customGasRef = useRef(null);

  const handleSlippageChange = (value) => {
    if (value === "custom") {
      setSlippage("custom");
      setShowCustomSlippage(true);

      setTimeout(() => {
        if (customSlippageRef.current) {
          customSlippageRef.current.focus();
        }
      }, 50);
    } else {
      setSlippage(value);
      setShowCustomSlippage(false);

      const slippageValue = value === "auto" ? 2 : parseFloat(value);
      updateData("savedSlippage", value);

      updateData("savedSlippageValue", slippageValue);
    }
  };

  const handleCustomSlippageChange = (e) => {
    let value = e.target.value;

    if (value !== "") {
      const numValue = parseFloat(value);
      if (numValue > 99) {
        value = "99";
      }
    }

    setCustomSlippageValue(value);

    if (value !== "") {
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue > 0) {
        updateData("savedSlippage", "custom");
        updateData("savedSlippageValue", numValue);
      }
    }
  };

  const handleCustomSlippageBlur = () => {
    let value = parseFloat(customSlippageValue);
    if (isNaN(value) || value <= 0) {
      value = 2;
      setCustomSlippageValue(value.toString());
    }

    updateData("savedSlippage", "custom");
    updateData("savedSlippageValue", value);
  };

  const handleAddedPriorityChange = (value) => {
    if (value === "custom") {
      setAddedPriority("custom");
      setShowCustomGas(true);

      setTimeout(() => {
        if (customGasRef.current) {
          customGasRef.current.focus();
        }
      }, 50);
    } else {
      setAddedPriority(value);
      setShowCustomGas(false);

      updateData("savedAddedPriority", value);

      if (value === "auto") {
        updateData("priorityGas", "2");
        console.log("Gas priority set to auto (2 gwei)");
      } else {
        updateData("priorityGas", value.toString());
        console.log(`Gas priority set to ${value} gwei`);
      }

      setTimeout(() => {
        const gasLevelRef = document.getElementById("gasLevelRef");
        if (gasLevelRef) {
          gasLevelRef.value = value === "auto" ? "2" : value.toString();
          const event = new Event("change", { bubbles: true });
          gasLevelRef.dispatchEvent(event);
        }
      }, 100);
    }
  };

  const handleCustomGasChange = (e) => {
    let value = e.target.value;

    if (value !== "") {
      const numValue = parseInt(value);
      if (numValue > 30) {
        value = "30";

        toast({
          type: "warning",
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "dark",
          message:
            "Gas values above 30 gwei may cause transaction failures. Value capped at 30 gwei.",
        });
      }
    }

    setCustomGasValue(value);

    if (value !== "") {
      const numValue = parseInt(value);
      if (!isNaN(numValue) && numValue > 0) {
        updateData("savedAddedPriority", "custom");

        updateData("priorityGas", numValue.toString());

        console.log(`Custom gas value set to ${numValue} gwei`);
      }
    }
  };

  const handleCustomGasBlur = () => {
    let value = parseInt(customGasValue);
    if (isNaN(value) || value <= 0) {
      value = 5;
      setCustomGasValue(value.toString());
    }

    if (value > 30) {
      value = 30;
      setCustomGasValue("30");
    }

    updateData("savedAddedPriority", "custom");
    updateData("priorityGas", value.toString());

    console.log(`Custom gas value confirmed at ${value} gwei`);

    const gasLevelRef = document.getElementById("gasLevelRef");
    if (gasLevelRef) {
      gasLevelRef.value = value.toString();
      const event = new Event("change", { bubbles: true });
      gasLevelRef.dispatchEvent(event);
    }
  };

  useEffect(() => {
    if (savedSlippage.current !== slippage) {
      setSlippage(savedSlippage.current);
    }
    if (savedAddedPriority.current !== addedPriority) {
      setAddedPriority(savedAddedPriority.current);
    }
  }, [savedSlippage.current, savedAddedPriority.current]);

  const getSlippageDisplay = () => {
    if (slippage === "custom") {
      return `${customSlippageValue}%`;
    }
    return "Custom";
  };

  const getGasDisplay = () => {
    if (addedPriority === "custom") {
      return `${customGasValue}`;
    }
    return "Custom";
  };

  const containerVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25,
        mass: 1,
        staggerChildren: 0.1,
      },
    },
    exit: {
      opacity: 0,
      y: -20,
      scale: 0.95,
      transition: {
        duration: 0.3,
      },
    },
  };

  const sectionVariants = {
    hidden: {
      opacity: 0,
      x: -20,
      scale: 0.9,
    },
    visible: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 25,
        mass: 0.8,
        staggerChildren: 0.05,
      },
    },
  };

  const buttonVariants = {
    hidden: {
      opacity: 0,
      y: 10,
      scale: 0.9,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 700,
        damping: 30,
      },
    },
    tap: {
      scale: 0.95,
      transition: {
        duration: 0.1,
      },
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2,
      },
    },
  };

  const inputVariants = {
    hidden: {
      opacity: 0,
      scale: 0.8,
      y: 5,
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 25,
      },
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: 5,
      transition: {
        duration: 0.2,
      },
    },
  };

  return (
    <motion.div
      className="juiced-settings-container"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      layout
    >
      {/* Slippage Section */}
      <motion.div
        className="slippage-section"
        variants={sectionVariants}
        layout
      >
        <motion.div className="settings-label" variants={buttonVariants}>
          <div className="settings-icon-container">
            <SlippageIcon />
          </div>
          <span className="small-text">Slippage</span>
        </motion.div>
        <motion.div
          className="settings-options"
          variants={sectionVariants}
          layout
        >
          <motion.button
            className={`option-button ${slippage === "auto" ? "active" : ""}`}
            onClick={() => handleSlippageChange("auto")}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            layout
          >
            Auto
          </motion.button>
          <motion.button
            className={`option-button ${slippage === "2" ? "active" : ""}`}
            onClick={() => handleSlippageChange("2")}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            layout
          >
            2%
          </motion.button>
          <motion.button
            className={`option-button ${slippage === "7" ? "active" : ""}`}
            onClick={() => handleSlippageChange("7")}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            layout
          >
            7%
          </motion.button>
          <motion.button
            className={`option-button ${slippage === "12" ? "active" : ""}`}
            onClick={() => handleSlippageChange("12")}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            layout
          >
            12%
          </motion.button>
          <AnimatePresence mode="wait">
            {showCustomSlippage ? (
              <motion.div
                className="custom-input-inline"
                key="custom-slippage-input"
                variants={inputVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                layout
              >
                <motion.input
                  ref={customSlippageRef}
                  type="number"
                  className="custom-input-small"
                  value={customSlippageValue}
                  onChange={handleCustomSlippageChange}
                  onBlur={handleCustomSlippageBlur}
                  min="0.01"
                  max="99"
                  step="0.1"
                  placeholder="%"
                  layout
                />
              </motion.div>
            ) : (
              <motion.button
                key="custom-slippage-button"
                className={`option-button custom ${
                  slippage === "custom" ? "active" : ""
                }`}
                onClick={() => handleSlippageChange("custom")}
                variants={buttonVariants}
                whileTap="tap"
                whileHover="hover"
                layout
              >
                {slippage === "custom" ? getSlippageDisplay() : "Custom"}
              </motion.button>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>

      {/* Gas Section */}
      <motion.div className="gas-section" variants={sectionVariants} layout>
        <motion.div className="settings-label" variants={buttonVariants}>
          <div className="settings-icon-container">
            <SaverInfoIcon />
          </div>
          <span className="small-text">Priority</span>
        </motion.div>
        <motion.div
          className="settings-options"
          variants={sectionVariants}
          layout
        >
          <motion.button
            className={`option-button ${
              addedPriority === "auto" ? "active" : ""
            }`}
            onClick={() => handleAddedPriorityChange("auto")}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            layout
          >
            Auto
          </motion.button>
          <motion.button
            className={`option-button ${addedPriority === 2 ? "active" : ""}`}
            onClick={() => handleAddedPriorityChange(2)}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            layout
          >
            Average
          </motion.button>
          <motion.button
            className={`option-button ${addedPriority === 3 ? "active" : ""}`}
            onClick={() => handleAddedPriorityChange(3)}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            layout
          >
            Quick
          </motion.button>
          <motion.button
            className={`option-button ${addedPriority === 5 ? "active" : ""}`}
            onClick={() => handleAddedPriorityChange(5)}
            variants={buttonVariants}
            whileTap="tap"
            whileHover="hover"
            layout
          >
            Instant
          </motion.button>
          <AnimatePresence mode="wait">
            {showCustomGas ? (
              <motion.div
                className="custom-input-inline"
                key="custom-gas-input"
                variants={inputVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                layout
              >
                <motion.input
                  ref={customGasRef}
                  type="number"
                  className="custom-input-small"
                  value={customGasValue}
                  onChange={handleCustomGasChange}
                  onBlur={handleCustomGasBlur}
                  min="1"
                  max="99"
                  step="1"
                  placeholder="Gwei"
                  layout
                />
              </motion.div>
            ) : (
              <motion.button
                key="custom-gas-button"
                className={`option-button custom ${
                  addedPriority === "custom" ? "active" : ""
                }`}
                onClick={() => handleAddedPriorityChange("custom")}
                variants={buttonVariants}
                whileTap="tap"
                whileHover="hover"
                layout
              >
                {addedPriority === "custom" ? getGasDisplay() : "Custom"}
              </motion.button>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default JuicedModeSettings;
