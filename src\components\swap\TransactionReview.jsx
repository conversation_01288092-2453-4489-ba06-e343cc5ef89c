"use client";

import React, { useState, useEffect } from "react";
import "../../styles/transaction-review.css";

const TransactionReview = ({
  isOpen,
  onClose,
  fromToken,
  toToken,
  fromAmount,
  toAmount,
  fromUsdValue,
  toUsdValue,
  swapData,
  onConfirm,
  onApprove,
  savedSlippageValue,
  savedAddedPriority,
  provider,
  chainId,
  pendingTxStatus,
  transaction,
  inSwap,
  isApprovalNeeded = false,
  devMode = false,
}) => {
  const [currentStep, setCurrentStep] = useState(isApprovalNeeded ? 1 : 2);
  const [isProcessing, setIsProcessing] = useState(false);

  // Update step when approval status changes
  useEffect(() => {
    if (!isApprovalNeeded && currentStep === 1) {
      setCurrentStep(2);
      setIsProcessing(false);
    }
  }, [isApprovalNeeded, currentStep]);

  if (!isOpen) return null;

  const handleStartApproval = async () => {
    if (devMode) {
      if (currentStep < 2) {
        setCurrentStep(currentStep + 1);
      } else {
        setCurrentStep(isApprovalNeeded ? 1 : 2);
      }
      return;
    }

    if (!swapData) return;

    setIsProcessing(true);

    try {
      if (isApprovalNeeded && currentStep === 1) {
        setCurrentStep(1);
        if (onApprove) {
          await onApprove();
          // After successful approval, move to step 2 and reset processing state
          setCurrentStep(2);
          setIsProcessing(false);
        }
      } else {
        setCurrentStep(2);
        if (onConfirm) {
          await onConfirm();
        }
        // Reset processing state after swap attempt
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Transaction failed:", error);
      setIsProcessing(false);
    }
  };

  const handleStepClick = (step) => {
    if (devMode) {
      setCurrentStep(step);
    }
  };

  const getStepState = (stepNumber) => {
    if (stepNumber < currentStep) {
      return "completed";
    } else if (stepNumber === currentStep) {
      return "active";
    } else {
      return "pending";
    }
  };

  const formatUsdValue = (value) => {
    if (!value) return "0";
    return typeof value === "string"
      ? value.replace(/,/g, "")
      : value.toString();
  };

  const formatTokenAmount = (amount) => {
    if (!amount || amount === "0") return "0";
    const num = parseFloat(amount);
    if (isNaN(num)) return "0";

    if (num < 0.001) {
      return num.toFixed(8);
    }

    if (num >= 1) {
      return num.toFixed(4);
    }

    return num.toFixed(6);
  };

  return (
    <div className="transaction-review-overlay">
      <button onClick={onClose} className="transaction-close-button">
        ×
      </button>{" "}
      <div className="transaction-review-container">
        <div className="transaction-review-header">
          <div className="transaction-review-title">Review limit</div>
          <div className="transaction-get-help">Get help</div>
        </div>

        <div className="transaction-token-section-border">
          <div className="transaction-token-section">
            <div className="transaction-token-left">
              <div className="transaction-token-label">You pay</div>
            </div>
            <div className="transaction-token-right">
              <div className="transaction-token-right-inner">
                <div className="transaction-token-amount">
                  {formatTokenAmount(fromAmount)} {fromToken?.symbol || "ETH"}
                </div>
                <div className="transaction-token-usd">
                  ≈ ${formatUsdValue(fromUsdValue)}
                </div>
              </div>{" "}
              <div className="transaction-token-icon">
                <img
                  src={
                    fromToken?.logoURI ||
                    fromToken?.logo_uri ||
                    "https://placehold.co/28x28"
                  }
                  alt={fromToken?.symbol || "Token"}
                />
              </div>{" "}
            </div>
          </div>

          {/* You Receive Section */}
          <div className="transaction-token-section t-b">
            <div className="transaction-token-left">
              <div className="transaction-token-label">You receive</div>
            </div>
            <div className="transaction-token-right">
              <div className="transaction-token-right-inner">
                <div className="transaction-token-amount">
                  {formatTokenAmount(toAmount)} {toToken?.symbol || "AAVE"}
                </div>
                <div className="transaction-token-usd">
                  ≈ ${formatUsdValue(toUsdValue)}
                </div>
              </div>{" "}
              <div className="transaction-token-icon">
                <img
                  src={
                    toToken?.logoURI ||
                    toToken?.logo_uri ||
                    "https://placehold.co/28x28"
                  }
                  alt={toToken?.symbol || "Token"}
                />
              </div>{" "}
            </div>
          </div>
        </div>
        {/* Approval Process Section */}
        <div className="transaction-approval-section">
          {/* Step 1: Approve in wallet - Only show if approval is needed */}
          {isApprovalNeeded && (
            <div
              className={`transaction-approval-step ${getStepState(1)}`}
              onClick={() => handleStepClick(1)}
              style={{ cursor: devMode ? "pointer" : "default" }}
            >
              <div className="transaction-approval-step-icon">
                <img
                  src={
                    fromToken?.logoURI ||
                    fromToken?.logo_uri ||
                    "https://placehold.co/20x20"
                  }
                  alt="Token"
                  style={{
                    width: "28px",
                    height: "28px",
                    borderRadius: "50%",
                  }}
                />
              </div>
              <div className="transaction-approval-step-content">
                <div className="transaction-approval-step-title">
                  Approve in wallet
                </div>
                <div className="transaction-approval-help">
                  Why do I have to approve a token?
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Confirm swap */}
          <div
            className={`transaction-approval-step ${getStepState(2)}`}
            onClick={() => handleStepClick(2)}
            style={{ cursor: devMode ? "pointer" : "default" }}
          >
            <div className="transaction-approval-step-icon">
              <span style={{ color: "white", fontSize: "16px" }}>✓</span>
            </div>
            <div className="transaction-approval-step-content">
              <div className="transaction-approval-step-title">
                Confirm swap
              </div>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <button
          onClick={handleStartApproval}
          disabled={
            !devMode &&
            (isProcessing || inSwap || pendingTxStatus === "pending")
          }
          className="transaction-action-button"
        >
          {devMode
            ? `Dev Mode - Step ${currentStep}/2 (Click to cycle)`
            : pendingTxStatus === "pending"
            ? "Transaction Pending..."
            : pendingTxStatus === "confirmed"
            ? "Transaction Confirmed!"
            : pendingTxStatus === "failed"
            ? "Transaction Failed"
            : isProcessing || inSwap
            ? "Processing..."
            : isApprovalNeeded && currentStep === 1
            ? "Approve Token"
            : "Confirm Swap"}
        </button>

        {/* Dev Mode Controls */}
      </div>
    </div>
  );
};

export default TransactionReview;
