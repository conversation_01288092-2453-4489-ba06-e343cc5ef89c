/* Custom styles for the SaverInfo component */

.saver-text-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.saver-text-container:last-child {
  border-bottom: none;
}

.saver-text-left {
  font-size: 0.95rem;
  color: var(--text-gray);
  display: flex;
  align-items: center;
}

.saver-text-right {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text);
}

/* Gas type indicator */
.gas-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 5px;
}

.gas-indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

/* Route indicator styles */
.route-v3 {
  color: #00ff88;
  font-weight: 600;
}

.route-v2 {
  color: #ff6b35;
  font-weight: 600;
}

.route-v2-to-v3 {
  color: #ffd700;
  font-weight: 600;
}

/* Gas indicator dot colors */
.gas-auto {
  background-color: #00ff88;
}

.gas-average {
  background-color: #ffd700;
}

.gas-quick {
  background-color: #ff9500;
}

.gas-instant {
  background-color: #ff4757;
}

.gas-custom {
  background-color: #a55eea;
}

/* Light mode overrides */
.light .saver-text-container {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.light .saver-text-left {
  color: var(--light-text-grey);
}

.light .saver-text-right {
  color: #333;
}

/* General box styling for SaverInfo */
.general-box {
  padding: 15px;
  border-radius: 25px;
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  z-index: 4;
  overflow: visible;
  background: var(--box-inner);
  margin-top: 10px;
  margin-bottom: 10px;
}

/* Slippage badge styling */
.slippage-badge {
  background-color: #00ff88;
  color: #000;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 8px;
}

/* Network cost styling */
.network-cost-original {
  color: var(--text-gray);
  text-decoration: line-through;
  margin-left: 8px;
  font-size: 0.9rem;
}

/* Exchange rate header styling */
.saver-exchange-rate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.saver-exchange-rate-text {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text);
}

.saver-dropdown-arrow {
  font-size: 0.8rem;
  color: var(--text-gray);
  transition: transform 0.2s ease;
  transform: rotate(180deg);
}

.saver-dropdown-arrow.expanded {
  transform: rotate(0deg);
}

/* Details container */
.saver-details-container {
  padding-top: 8px;
}

/* Light mode overrides for new elements */
.light .saver-exchange-rate-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.light .saver-exchange-rate-text {
  color: #333;
}

.light .saver-dropdown-arrow {
  color: var(--light-text-grey);
}

.light .slippage-badge {
  background-color: #00ff88;
  color: #000;
}

.light .network-cost-original {
  color: var(--light-text-grey);
}
