/* Custom styles for the SaverInfo component */

.saver-text-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.saver-text-container:last-child {
  border-bottom: none;
}

.saver-text-left {
  font-size: 0.95rem;
  color: var(--text-gray);
  display: flex;
  align-items: center;
}

.saver-text-right {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text);
}

/* Gas type indicator */
.gas-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: 5px;
}

.gas-indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

/* Route indicator styles */
.route-v3 {
  color: #00ff88;
  font-weight: 600;
}

.route-v2 {
  color: #ff6b35;
  font-weight: 600;
}

.route-v2-to-v3 {
  color: #ffd700;
  font-weight: 600;
}

/* Gas indicator dot colors */
.gas-auto {
  background-color: #00ff88;
}

.gas-average {
  background-color: #ffd700;
}

.gas-quick {
  background-color: #ff9500;
}

.gas-instant {
  background-color: #ff4757;
}

.gas-custom {
  background-color: #a55eea;
}

/* Light mode overrides */
.light .saver-text-container {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.light .saver-text-left {
  color: var(--light-text-grey);
}

.light .saver-text-right {
  color: #333;
}

/* General box styling for SaverInfo */
.general-box {
  margin-bottom: 15px;
}

/* Slippage badge styling */
.slippage-badge {
  background-color: #00ff88;
  color: #000;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-right: 8px;
}

/* Network cost styling */
.network-cost-original {
  color: var(--text-gray);
  text-decoration: line-through;
  margin-left: 8px;
  font-size: 0.9rem;
}

/* Light mode overrides for new elements */
.light .slippage-badge {
  background-color: #00ff88;
  color: #000;
}

.light .network-cost-original {
  color: var(--light-text-grey);
}
