import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";

// RPC URLs for different chains
const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }

    const authenticatedAddress = authResult.address;

    const body = await request.json();
    const { chain_id } = body;

    if (!chain_id || !RPC_URLS[chain_id]) {
      return NextResponse.json(
        { error: "Invalid or missing chain_id" },
        { status: 400 }
      );
    }

    const provider = new ethers.JsonRpcProvider(RPC_URLS[chain_id]);

    let feeData;
    try {
      feeData = await provider.getFeeData();
    } catch (error) {
      console.warn("Error getting fee data:", error);

      feeData = { gasPrice: await provider.getGasPrice() };
    }

    const latestBlock = await provider.getBlock("latest");

    const gasPrice = {
      blockPrices: [
        {
          blockNumber: latestBlock ? latestBlock.number : 0,
          baseFeePerGas:
            latestBlock && latestBlock.baseFeePerGas
              ? ethers.formatUnits(latestBlock.baseFeePerGas, "gwei")
              : feeData.gasPrice
              ? ethers.formatUnits(feeData.gasPrice, "gwei")
              : "20",
          estimatedPrices: [
            {
              confidence: 99,
              maxFeePerGas: feeData.maxFeePerGas
                ? ethers.formatUnits(feeData.maxFeePerGas, "gwei")
                : ethers.formatUnits(
                    feeData.gasPrice || ethers.parseUnits("50", "gwei"),
                    "gwei"
                  ),
              maxPriorityFeePerGas: feeData.maxPriorityFeePerGas
                ? ethers.formatUnits(feeData.maxPriorityFeePerGas, "gwei")
                : "2",
              price: feeData.gasPrice
                ? ethers.formatUnits(feeData.gasPrice, "gwei")
                : "50",
            },
            {
              confidence: 95,
              maxFeePerGas: feeData.maxFeePerGas
                ? ethers.formatUnits(feeData.maxFeePerGas, "gwei")
                : ethers.formatUnits(
                    feeData.gasPrice || ethers.parseUnits("40", "gwei"),
                    "gwei"
                  ),
              maxPriorityFeePerGas: feeData.maxPriorityFeePerGas
                ? ethers.formatUnits(feeData.maxPriorityFeePerGas, "gwei")
                : "1.5",
              price: feeData.gasPrice
                ? ethers.formatUnits(feeData.gasPrice, "gwei")
                : "40",
            },
            {
              confidence: 70,
              maxFeePerGas: feeData.maxFeePerGas
                ? ethers.formatUnits(feeData.maxFeePerGas, "gwei")
                : ethers.formatUnits(
                    feeData.gasPrice || ethers.parseUnits("30", "gwei"),
                    "gwei"
                  ),
              maxPriorityFeePerGas: feeData.maxPriorityFeePerGas
                ? ethers.formatUnits(feeData.maxPriorityFeePerGas, "gwei")
                : "1",
              price: feeData.gasPrice
                ? ethers.formatUnits(feeData.gasPrice, "gwei")
                : "30",
            },
          ],
        },
      ],
      gasPrice: feeData.gasPrice ? feeData.gasPrice.toString() : null,
      maxFeePerGas: feeData.maxFeePerGas
        ? feeData.maxFeePerGas.toString()
        : null,
      maxPriorityFeePerGas: feeData.maxPriorityFeePerGas
        ? feeData.maxPriorityFeePerGas.toString()
        : null,
    };

    return NextResponse.json({ gasPrice });
  } catch (error) {
    console.warn("Error fetching gas price:", error);
    return NextResponse.json(
      { error: "Failed to fetch gas price" },
      { status: 500 }
    );
  }
}
