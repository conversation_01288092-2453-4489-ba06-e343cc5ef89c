/* Custom styles for react-hot-toast to match previous toast styles */

/* Toast container */
div[id^="react-hot-toast"] {
  width: 380px;
}

/* Toast styling - enhanced clean and minimal */
.react-hot-toast {
  background: linear-gradient(95deg, #262a2f 0%, #181a1d 100%) !important;
  color: #ffffff !important;
  border-radius: 20px !important;
  padding: 16px 20px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5) !important;
  border: 1px solid rgba(42, 45, 49, 0.8) !important;
  min-height: 80px !important;
  margin-bottom: 16px !important;
  font-family: Basel, -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI",
    Roboto, Helvetica, Arial, sans-serif !important;

  /* Add smooth animation */
  transition: all 0.4s ease-out !important;
  transform: translateX(0) !important;

  /* Add subtle glow effect */
  position: relative !important;
  overflow: hidden !important;
}

/* Add subtle gradient overlay */
.react-hot-toast::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 1px !important;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  ) !important;
}

/* Toast body */
.react-hot-toast > div {
  padding: 4px 0 !important;
  font-size: 1.2rem !important;
  line-height: 1.5 !important;
  font-weight: 500 !important;
  align-items: center !important;
  display: flex !important;
  z-index: 1 !important;
  position: relative !important;
}

/* Custom close button */
.react-hot-toast button {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 1.8rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  line-height: 1 !important;
  margin-left: 12px !important;
  font-weight: 300 !important;
  z-index: 2 !important;
}

.react-hot-toast button:hover {
  color: #ffffff !important;
  transform: scale(1.1) !important;
}

/* Toast icons for different types */
.react-hot-toast svg {
  margin-right: 12px !important;
  height: 22px !important;
  width: 22px !important;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.3)) !important;
}

.success-toast {
  border-left: 4px solid #fce025 !important;
}

.success-toast svg {
  color: #fce025 !important;
}

.error-toast {
  border-left: 4px solid #ff4e4e !important;
}

.error-toast svg {
  color: #ff4e4e !important;
}

.info-toast {
  border-left: 4px solid #3498db !important;
}

.info-toast svg {
  color: #3498db !important;
}

.warning-toast {
  border-left: 4px solid #f1c40f !important;
}

.warning-toast svg {
  color: #f1c40f !important;
}

/* Animation for toasts - enhanced smooth animations */
@keyframes toast-enter {
  0% {
    transform: translateX(120%);
    opacity: 0;
    filter: blur(4px);
  }
  15% {
    transform: translateX(-5%);
    opacity: 0.8;
    filter: blur(0);
  }
  30% {
    transform: translateX(2%);
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes toast-exit {
  0% {
    transform: translateX(0);
    opacity: 1;
    filter: blur(0);
  }
  15% {
    transform: translateX(-5%);
    opacity: 0.9;
  }
  100% {
    transform: translateX(120%);
    opacity: 0;
    filter: blur(4px);
  }
}

/* Apply enhanced animations to toast elements */
[data-react-hot-toast][data-enter] {
  animation: toast-enter 0.6s cubic-bezier(0.21, 1.02, 0.73, 1) forwards !important;
}

[data-react-hot-toast][data-leave] {
  animation: toast-exit 0.6s cubic-bezier(0.06, 0.71, 0.55, 1) forwards !important;
}

/* Mobile styles */
@media only screen and (max-width: 480px) {
  div[id^="react-hot-toast"] {
    width: calc(100% - 24px) !important;
    padding: 0 !important;
    left: 12px !important;
    right: 12px !important;
    margin: 0 !important;
  }

  .react-hot-toast {
    margin-bottom: 8px !important;
  }
}
