import { NextResponse } from "next/server";
import { ethers } from "ethers";
import { rpcAuthMiddleware } from "../../middleware";
import uniswapRouterABI from "../../../../constants/abis/UniswapRouter.json";
import { CHAINS } from "../../../../constants/constants";

const RPC_URLS = {
  1:
    process.env.ETHEREUM_RPC_URL ||
    "https://ethereum-mainnet.core.chainstack.com/c0a7bbcdaea477c287dc20106b73a463",
  8453: process.env.BASE_RPC || "https://mainnet.base.org",
};

export async function POST(request) {
  try {
    const authResult = await rpcAuthMiddleware(request);

    if (!authResult.success) {
      console.warn("Authentication failed");
      return authResult.response;
    }

    const authenticatedAddress = authResult.address;

    const body = await request.json();
    const { chain_id, amountIn, path, uniswapRouterAddress } = body;

    if (
      !chain_id ||
      !amountIn ||
      !path ||
      !Array.isArray(path) ||
      path.length < 2
    ) {
      return NextResponse.json(
        {
          error:
            "Chain ID, amount in, and path (array with at least 2 addresses) are required",
        },
        { status: 400 }
      );
    }

    console.log(
      `Fetching amounts out for path ${path.join(" -> ")} on chain ${chain_id}`
    );
    console.log(`Amount in: ${amountIn}`);
    console.log(`Router address: ${uniswapRouterAddress}`);

    const rpcUrl = RPC_URLS[chain_id];
    if (!rpcUrl) {
      return NextResponse.json(
        { error: "Unsupported chain ID" },
        { status: 400 }
      );
    }

    const provider = new ethers.JsonRpcProvider(rpcUrl);

    const routerContract = new ethers.Contract(
      uniswapRouterAddress,
      uniswapRouterABI,
      provider
    );

    const amounts = await routerContract.getAmountsOut(amountIn, path);

    console.log(
      `Swap amounts: ${JSON.stringify(amounts.map((a) => a.toString()))}`
    );

    return NextResponse.json({
      amounts: amounts.map((amount) => amount.toString()),
    });
  } catch (error) {
    console.warn("RPC error:", error);
    return NextResponse.json(
      { error: "Failed to get amounts out: " + error.message },
      { status: 500 }
    );
  }
}
