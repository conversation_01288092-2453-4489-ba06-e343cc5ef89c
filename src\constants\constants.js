export const MAX_ALLOWANCE =
  115792089237316195423570985008687907853269984665640564039457584007913129639935n;

export const exchangeProxy = "******************************************";

const ETH_TOKENS_DISPLAY = {
  1: {
    is_v2: true,
    is_partner: true,
    chain_id: 1,
    name: "E<PERSON>",
    symbol: "ETH",
    decimals: 18,
    address: "******************************************",
    logo_uri: "https://cdn.worldvectorlogo.com/logos/ethereum-eth.svg",
    dollarValue: 0,
  },
  /*   2: {
    chain_id: 1,
    is_partner: true,
    name: "Wrapped Ether",
    symbol: "WETH",
    decimals: 18,
    address: "******************************************",
    logo_uri: "https://cdn.worldvectorlogo.com/logos/ethereum-eth.svg",
    dollarValue: 0,
  }, */
  3: {
    is_partner: true,
    is_v2: true,
    chain_id: 1,
    name: "USD Coin",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logo_uri:
      "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png",
  },

  2: {
    is_partner: true,
    is_v2: true,

    //  isPromo: true,
    chain_id: 1,
    name: "Pineapple",
    address: "******************************************",
    symbol: "PAPPLE",
    decimals: 9,
    dollarValue: 0,
    logo_uri:
      "https://www.dextools.io/resources/tokens/logos/ether/******************************************.jpg?1741381007569",
  },

  4: {
    is_partner: true,
    is_v2: true,
    chain_id: 1,
    name: "PEPE",
    symbol: "PEPE",
    decimals: 18,
    address: "******************************************",
    logo_uri:
      "https://dd.dexscreener.com/ds-data/tokens/ethereum/******************************************.png?size=lg&key=7d789c",
    dollarValue: 0,
  },

  5: {
    is_partner: true,
    is_v2: true,
    chain_id: 1,
    name: "Kekius Maximus",
    symbol: "KEKIUS",
    decimals: 9,
    address: "******************************************",
    logo_uri:
      "https://dd.dexscreener.com/ds-data/tokens/ethereum/******************************************.png?size=lg&key=7d789c",
    dollarValue: 0,
  },
};
const BASE_TOKENS = {
  1: {
    is_v2: true,

    id: 1,
    chain_id: 8453,
    is_partner: true,
    name: "Ether",
    symbol: "ETH",
    decimals: 18,
    address: "******************************************",
    logo_uri:
      "https://icons.iconarchive.com/icons/cjdowner/cryptocurrency-flat/512/Ethereum-ETH-icon.png",
  },
  /*   2: {
    id: 2,
    chain_id: 8453,
    name: "Wrapped Ether",
    is_partner: true,
    symbol: "WETH",
    decimals: 18,
    address: "******************************************",
    logo_uri: "https://www.dextools.io/resources/chains/med/ether.png",
  }, */
  2: {
    id: 2,
    is_v2: true,

    is_partner: true,
    chain_id: 8453,
    name: "USDC",
    symbol: "USDC",
    decimals: 6,
    address: "******************************************",
    logo_uri: "https://ethereum-optimism.github.io/data/USDC/logo.png",
  },

  3: {
    id: 3,
    is_v2: true,

    chain_id: 8453,
    is_partner: false,
    name: "aixbt by Virtuals",
    symbol: "AIXBT",
    decimals: 18,
    address: "******************************************",
    logo_uri:
      "https://assets.coingecko.com/coins/images/35529/standard/1000050750.png",
  },

  8: {
    id: 8,
    chain_id: 8453,
    is_v2: true,

    is_partner: false,
    name: "Brett",
    symbol: "BRETT",
    decimals: 18,
    address: "******************************************",
    logo_uri:
      "https://assets.coingecko.com/coins/images/35529/standard/1000050750.png",
  },

  14: {
    id: 14,
    chain_id: 8453,
    is_v2: true,

    is_partner: true,
    name: "Chainlink",
    symbol: "LINK",
    decimals: 18,
    address: "******************************************",
    logo_uri:
      "https://assets.coingecko.com/coins/images/877/small/chainlink-new-logo.png",
  },
};

const CHAINS = {
  1: {
    name: "Ethereum",
    chain_id: 1,
    usdcAddress: "******************************************",
    wethAddress: "******************************************",
    ethAddress: "******************************************",
    uniswapRouterAddressV2: "******************************************",
    uniswapFactoryAddressV2: "******************************************",
    uniswapRouterAddressV3: "******************************************",
    uniswapQuoterV3: "******************************************",
    pappleRouterAddress: "******************************************",

    nativeSymbol: "ETH",
  },
  8453: {
    name: "Base",
    chain_id: 8453,
    usdcAddress: "******************************************",
    wethAddress: "******************************************",
    ethAddress: "******************************************",
    uniswapRouterAddressV2: "******************************************",
    uniswapFactoryAddressV2: "******************************************",
    uniswapRouterAddressV3: "******************************************",
    uniswapFactoryAddressV3: "******************************************",
    uniswapQuoterV3: "******************************************",
    pappleRouterAddress: "******************************************",

    nativeSymbol: "ETH",
  },
  56: {
    name: "BSC",
    chain_id: 56,
    usdcAddress: "******************************************",
    wethAddress: "******************************************",
    ethAddress: "******************************************",
    uniswapRouterAddressV2: "******************************************",
    uniswapFactoryAddressV2: "******************************************",
    uniswapRouterAddressV3: "******************************************",
    uniswapFactoryAddressV3: "******************************************",
    uniswapQuoterV3: "******************************************",
    //
    /*     pappleRouterAddress: "",
     */ nativeSymbol: "BNB",
  },
};
export { ETH_TOKENS_DISPLAY };
export { BASE_TOKENS };
export { CHAINS };
