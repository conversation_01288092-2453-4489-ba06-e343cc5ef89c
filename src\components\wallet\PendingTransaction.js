import React, { useState, useEffect, memo } from "react";
import Loader from "../common/Loader";

function PendingTransaction({
  transaction,
  chainId,
  devMode,
  devStatus,
  onClose,
  swapData,
  isInFlow = false, // New prop to indicate if component is inside TransactionFlow
}) {
  console.log("PendingTransaction component mounted with:", {
    transaction,
    chainId,
    devMode,
    devStatus,
    swapData,
    isInFlow,
  });

  const [status, setStatus] = useState(
    devMode ? devStatus || "waiting" : "waiting"
  );

  console.log("PendingTransaction status:", status);
  const [isClosing, setIsClosing] = useState(false);

  const mockHash =
    "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef";
  const hash =
    transaction !== null ? transaction.hash : devMode ? mockHash : "";

  let explorerLink = "";
  if (chainId === 1) {
    explorerLink = `https://etherscan.io/tx/${hash}`;
  } else if (chainId === 8453) {
    explorerLink = `https://basescan.org/tx/${hash}`;
  } else if (chainId === 56) {
    explorerLink = `https://bscscan.com/tx/${hash}`;
  }

  const [animating, setAnimating] = useState(false);

  const cycleStatus = () => {
    return;
    if (devMode && !animating) {
      setAnimating(true);

      setTimeout(() => {
        if (status === "waiting") setStatus("pending");
        else if (status === "pending") setStatus("confirmed");
        else if (status === "confirmed") setStatus("failed");
        else if (status === "failed") setStatus("waiting");

        setTimeout(() => {
          setAnimating(false);
        }, 300);
      }, 300);
    }
  };

  useEffect(() => {
    console.log("PendingTransaction - devStatus changed:", devStatus);
    console.log("PendingTransaction - devMode:", devMode);

    if (devMode && devStatus) {
      console.log("Setting status to:", devStatus);
      setStatus(devStatus);
    }
  }, [devMode, devStatus]);

  useEffect(() => {
    console.log("PendingTransaction mounted with status:", status);
    return () => {
      console.log("PendingTransaction unmounted");
    };
  }, []);

  const [transactionHash, setTransactionHash] = useState(
    transaction ? transaction.hash : null
  );

  useEffect(() => {
    if (transaction && transaction.hash !== transactionHash) {
      setTransactionHash(transaction.hash);
    }
  }, [transaction, transactionHash]);

  useEffect(() => {
    if (transaction !== null && !devMode) {
      if (status === "waiting") {
        setStatus("pending");
      }

      let isMounted = true;

      const listenForConfirmation = async () => {
        try {
          const txReceipt = await transaction.wait();

          if (isMounted) {
            if (txReceipt.status === 1) {
              setStatus("confirmed");

              setTimeout(() => {
                handleClose();
              }, 3000);
            } else {
              setStatus("failed"); // transaction failed
            }
          }
        } catch (error) {
          console.warn("Error waiting for transaction confirmation:", error);
          if (isMounted) {
            setStatus("failed"); // transaction failed
          }
        }
      };

      listenForConfirmation();

      return () => {
        isMounted = false;
      };
    }
  }, [transactionHash, devMode]);

  const handleClose = () => {
    console.log("PendingTransaction handleClose called, status:", status);

    console.log("Allowing close of PendingTransaction");

    setIsClosing(true);

    if (onClose) {
      onClose();
    }
  };

  if (isClosing) {
    return null;
  }

  return (
    <div className="pending-container">
      <div className="pending-box">
        <div className="close-button" onClick={handleClose}>
          ×
        </div>

        <div
          className="pending-content"
          onClick={devMode ? cycleStatus : undefined}
          style={{
            cursor: devMode ? "pointer" : "default",
          }}
        >
          {!animating && status === "waiting" && (
            <div key="waiting">
              <Loader size="medium" />
              <p className="pending-text">
                Please confirm this transaction in your wallet
              </p>
            </div>
          )}

          {/*           {!animating && status === "pending" && (
            <div key="pending">
              <Loader size="medium" />
              <p className="pending-text">
                Your transaction has been submitted
              </p>
            </div>
          )} */}

          {!animating && status === "confirmed" && (
            <div key="confirmed">
              <div className="status-icon">
                <svg
                  width="64"
                  height="64"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle
                    cx="12"
                    cy="12"
                    r="10"
                    fill="var(--primary-color)"
                    opacity="0.2"
                  />
                  <path
                    d="M8.5 12.5L10.5 14.5L15.5 9.5"
                    stroke="var(--primary-color)"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <p className="pending-text">
                Your transaction has been confirmed!
              </p>
            </div>
          )}

          {!animating && status === "failed" && (
            <div key="failed">
              <div className="status-icon">
                <svg
                  width="64"
                  height="64"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="12" cy="12" r="10" fill="#ff3333" opacity="0.2" />
                  <path
                    d="M15 9L9 15M9 9L15 15"
                    stroke="#ff3333"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <p className="pending-text">Your transaction has failed</p>
            </div>
          )}
        </div>

        <a
          className="explorer-button"
          href={devMode ? "#" : explorerLink}
          target={devMode ? "_self" : "_blank"}
          rel="noreferrer"
          onClick={devMode ? (e) => e.preventDefault() : undefined}
        >
          View on Explorer
        </a>
      </div>
    </div>
  );
}

export default memo(PendingTransaction);
