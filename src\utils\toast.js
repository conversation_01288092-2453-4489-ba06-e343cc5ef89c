import toast from "react-hot-toast";

/**
 * Custom toast utility functions to provide consistent toast notifications
 * with the same look and feel as the previous implementation.
 */
const customToast = {
  /**
   * Show a success toast notification
   * @param {string} message - The message to display
   * @param {Object} options - Additional options for the toast
   */
  success: (message, options = {}) => {
    return toast.success(message, {
      className: "react-hot-toast success-toast",
      ...options,
    });
  },

  /**
   * Show an error toast notification
   * @param {string} message - The message to display
   * @param {Object} options - Additional options for the toast
   */
  error: (message, options = {}) => {
    return toast.error(message, {
      className: "react-hot-toast error-toast",
      ...options,
    });
  },

  /**
   * Show an info toast notification
   * @param {string} message - The message to display
   * @param {Object} options - Additional options for the toast
   */
  info: (message, options = {}) => {
    return toast(message, {
      className: "react-hot-toast info-toast",
      icon: "ℹ️",
      ...options,
    });
  },

  /**
   * Show a warning toast notification
   * @param {string} message - The message to display
   * @param {Object} options - Additional options for the toast
   */
  warning: (message, options = {}) => {
    return toast(message, {
      className: "react-hot-toast warning-toast",
      icon: "⚠️",
      ...options,
    });
  },

  /**
   * Show a loading toast notification
   * @param {string} message - The message to display
   * @param {Object} options - Additional options for the toast
   */
  loading: (message, options = {}) => {
    return toast.loading(message, {
      className: "react-hot-toast info-toast",
      ...options,
    });
  },

  /**
   * Show a promise toast notification
   * @param {Promise} promise - The promise to track
   * @param {Object} messages - Messages for loading, success, and error states
   * @param {Object} options - Additional options for the toast
   */
  promise: (promise, messages, options = {}) => {
    return toast.promise(promise, messages, options);
  },

  /**
   * Dismiss a toast notification
   * @param {string} toastId - The ID of the toast to dismiss
   */
  dismiss: (toastId) => {
    toast.dismiss(toastId);
  },
};

export default customToast;
